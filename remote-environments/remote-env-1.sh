#!/bin/bash
set -e

echo "🚀 Installing layout manager dependencies..."

# Navigate to the simulator app
cd /mnt/persist/workspace/apps/simulator

# Install golden-layout and react-mosaic-component
echo "📦 Installing golden-layout..."
pnpm add golden-layout

echo "📦 Installing react-mosaic-component..."
pnpm add react-mosaic-component

echo "📦 Installing additional type definitions..."
pnpm add -D @types/golden-layout

echo "✅ Dependencies installed successfully!"
echo "📍 Installed packages:"
echo "  - golden-layout"
echo "  - react-mosaic-component"
echo "  - @types/golden-layout"