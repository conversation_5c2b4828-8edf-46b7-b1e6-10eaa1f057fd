{"name": "@au25/storybook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:simulator": "tsx src/simulator/examples/clock-auction-example.ts"}, "dependencies": {"@types/uuid": "^10.0.0", "@visx/axis": "^3.12.0", "@visx/curve": "^3.12.0", "@visx/gradient": "^3.12.0", "@visx/grid": "^3.12.0", "@visx/group": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "ag-grid-community": "33.3.1", "ag-grid-react": "33.3.1", "flexlayout-react": "^0.8.17", "plotly.js": "^2.35.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.1", "react-plotly.js": "^2.6.0", "recharts": "^2.15.3", "tailwindcss": "^3.4.17", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@storybook/addon-a11y": "^8.6.14", "@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/test": "^8.6.14", "@types/plotly.js": "^2.35.6", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/react-plotly.js": "^2.6.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-storybook": "^0.11.1", "globals": "^16.2.0", "storybook": "^8.6.14", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5", "vitest": "^3.1.4"}}