import "./App.css";
import AppShell from "./layouts/AppShell";
import AuctionToolbar from "./components/AuctionToolbar";
import Original from "./layouts/OriginalPanel";
import FlexLayoutPanel from "./layouts/FlexLayoutPanel";
import GoldenLayoutPanel from "./layouts/GoldenLayoutPanel";
import ReactMosaicPanel from "./layouts/ReactMosaic";
import { useState } from "react";

function App() {
  const [panel, setPanel] = useState("original");

  const toolbar = (
    <AuctionToolbar
      currentLayout={panel}
      onLayoutChange={setPanel}
    />
  );

  const main = (
    <>
      {panel === "original" && <Original />}
      {panel === "flex" && <FlexLayoutPanel />}
      {panel === "golden" && <GoldenLayoutPanel />}
      {panel === "reactMosaic" && <ReactMosaicPanel />}
    </>
  );

  return <AppShell toolbar={toolbar} main={main} />;
}

export default App;