import "./App.css";
import Original from "./layouts/OriginalPanel";
import FlexLayoutPanel from "./layouts/FlexLayoutPanel";
import GoldenLayoutPanel from "./layouts/GoldenLayoutPanel";
import ReactMosaicPanel from "./layouts/ReactMosaic";
import { useState } from "react";

function App() {
  const [panel, setPanel] = useState("original");
  return (
    <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start", height: "100vh" }}>
      <div style={{ marginBottom: "1rem" }}>
        <button onClick={() => setPanel("original")}>Original</button>
        <button onClick={() => setPanel("flex")}>Flex Layout</button>
        <button onClick={() => setPanel("golden")}>Golden Layout</button>
        <button onClick={() => setPanel("reactMosaic")}>React Mosaic</button>
      </div>
      <div style={{ width: "100%", flex: 1 }}>
        {panel === "original" && <Original />}
        {panel === "flex" && <FlexLayoutPanel />}
        {panel === "golden" && <GoldenLayoutPanel />}
        {panel === "reactMosaic" && <ReactMosaicPanel />}
      </div>
    </div>
  );
}

export default App;
