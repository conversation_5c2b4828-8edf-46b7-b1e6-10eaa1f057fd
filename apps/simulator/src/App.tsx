import "./App.css";
import Original from "./layouts/OriginalPanel";
import FlexLayoutPanel from "./layouts/FlexLayoutPanel";
import GoldenLayoutPanel from "./layouts/GoldenLayoutPanel";
import ReactMosaicPanel from "./layouts/ReactMosaic";
import { useState } from "react";

function App() {
  const [panel, setPanel] = useState("original");
  
  const layouts = [
    { id: "original", name: "Original", description: "Fixed layout with ResizableDashboard" },
    { id: "flex", name: "FlexLayout", description: "Flexible drag & drop layout" },
    { id: "golden", name: "Golden Layout", description: "Professional layout manager" },
    { id: "reactMosaic", name: "React Mosaic", description: "Tiling window manager" },
  ];

  return (
    <div style={{ display: "flex", flexDirection: "column", height: "100vh" }}>
      <div id= style={{ 
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        padding: "1rem 2rem",
        boxShadow: "0 2px 10px rgba(0,0,0,0.1)"
      }}>
        <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", maxWidth: "1200px", margin: "0 auto" }}>
          <div>
            <h1 style={{ color: "white", margin: "0 0 0.5rem 0", fontSize: "1.5rem", fontWeight: "bold" }}>🕐 Auction Simulator</h1>
            <p style={{ color: "rgba(255,255,255,0.9)", margin: 0, fontSize: "0.9rem" }}>Choose your preferred layout manager</p>
          </div>
          <div style={{ display: "flex", gap: "0.5rem" }}>
            {layouts.map(({ id, name, description }) => (
              <button
                key={id}
                onClick={() => setPanel(id)}
                style={{
                  padding: "0.75rem 1.5rem",
                  border: "2px solid rgba(255,255,255,0.3)",
                  borderRadius: "8px",
                  background: panel === id ? "rgba(255,255,255,0.2)" : "rgba(255,255,255,0.1)",
                  color: "white",
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  fontSize: "0.9rem",
                  fontWeight: panel === id ? "bold" : "normal",
                }}
                title={description}
              >
                {name}
              </button>
            ))}
          </div>
        </div>
      </div>
      <div id="main" style={{ flex: 1, overflow: "hidden" }}>
        {panel === "original" && <Original />}
        {panel === "flex" && <FlexLayoutPanel />}
        {panel === "golden" && <GoldenLayoutPanel />}
        {panel === "reactMosaic" && <ReactMosaicPanel />}
      </div>
    </div>
  );
}

export default App;