import React from "react";
import { useAuctionStore } from "../store/useAuctionStore";

interface AuctionToolbarProps {
  currentLayout: string;
  onLayoutChange: (layout: string) => void;
}

function AuctionToolbar({
  currentLayout,
  onLayoutChange,
}: AuctionToolbarProps) {
  const {
    currentRound,
    isRunning,
    simulator,
    auctionResult,
    startNewAuction,
    runNextRound,
    runFullAuction,
  } = useAuctionStore();

  const layouts = [
    { id: "original", name: "Original" },
    { id: "flex", name: "FlexLayout" },
    { id: "golden", name: "Golden Layout" },
    { id: "reactMosaic", name: "React Mosaic" },
  ];

  const currentLayoutName =
    layouts.find((l) => l.id === currentLayout)?.name || "Unknown";

  return (
    <div
      className="flex w-full items-center gap-4 px-4 py-2 shadow-md"
      style={{
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      }}
    >
      {/* --- Left Side Items --- */}

      {/* The Title (using a span as requested) */}
      <span className="text-xl font-bold text-white flex-shrink-0">
        🕐 Auction Simulator -
      </span>

      {/* Current layout name */}
      <span className="text-sm text-white font-medium opacity-90 flex-shrink-0">
        {currentLayoutName} - &nbsp;
      </span>

      {/* The Layout Buttons (no wrapper div, they are now independent) */}
      {layouts.map((layout) => (
        <button
          key={layout.id}
          onClick={() => onLayoutChange(layout.id)}
          className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
            currentLayout === layout.id
              ? "bg-white text-blue-600 shadow-sm"
              : "text-white hover:text-white hover:bg-white hover:bg-opacity-20 border border-white border-opacity-30"
          }`}
        >
          {layout.name}
        </button>
      ))}

      {/* --- Right Side Items --- */}

      {/* 
    THE KEY: `ml-auto` (margin-left: auto)
    This is applied to the FIRST element you want pushed to the right. 
    It automatically takes up all available space to its left, shoving itself 
    and everything after it to the end of the flex container.
  */}
      <div style={{ width: "10px", display: "inline-block" }}></div>

      <button
        onClick={startNewAuction}
        disabled={isRunning}
        className="ml-auto bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
      >
        🚀 Start New Auction
      </button>

      <div style={{ width: "10px", display: "inline-block" }}></div>

      {/* The other control buttons just follow normally */}
      {simulator && !auctionResult && (
        <>
          <button
            onClick={runNextRound}
            disabled={isRunning || simulator.isAuctionComplete()}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
          >
            ⏭️ Next Round
          </button>

          <button
            onClick={runFullAuction}
            disabled={isRunning || simulator.isAuctionComplete()}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
          >
            ⚡ Run to Completion
          </button>
        </>
      )}

      {/* The Round Indicator (using a span as a mini flex container to group its children) */}
      <span className="flex items-center gap-2 text-sm text-white bg-white bg-opacity-20 px-3 py-1.5 rounded-lg flex-shrink-0">
        <span className="font-medium">Round:</span>
        <span className="bg-white text-gray-800 px-2 py-0.5 rounded font-mono">
          {currentRound}
        </span>
        {isRunning && (
          <span className="text-yellow-300 animate-pulse ml-2">
            ● Running...
          </span>
        )}
      </span>
    </div>
  );
}

export default AuctionToolbar;
