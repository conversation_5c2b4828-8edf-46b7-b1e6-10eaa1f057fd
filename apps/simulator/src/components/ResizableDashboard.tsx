import React, { useState, useMemo } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { PriceTimelineChart } from './charts/PriceTimelineChart';
import { SupplyDemandChart } from './charts/SupplyDemandChart';
import { VolumeAnalysisChart } from './charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from './charts/ConstraintHeatmap';
import { AllocationFlowChart } from './charts/AllocationFlowChart';
import { ParticipantsTable } from './ParticipantsTable';
import type { ClockAuctionResult, ClockAuctionRound, ClockParticipant } from '../types/types';

// Make ResponsiveGridLayout responsive to container width
const ResponsiveGridLayout = WidthProvider(Responsive);

interface ResizableDashboardProps {
  rounds: ClockAuctionRound[];
  auctionResult: ClockAuctionResult | null;
  currentRound: number;
  participants: ClockParticipant[];
}

export function ResizableDashboard({
  rounds,
  auctionResult,
  participants
}: ResizableDashboardProps) {

  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <div className="text-gray-500 text-lg mb-2">
            🎯 Ready to start auction simulation
          </div>
          <p className="text-gray-400">
            Click "Start New Auction" to begin the double-sided clock auction
          </p>
        </div>
      </div>
    );
  }

  const latestRound = rounds[rounds.length - 1];
  const currentPrice = latestRound.announcedPrice;

  // Define the default layout for all widgets
  const defaultLayouts = {
    lg: [
      // Status cards in top row
      { i: 'price', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
      { i: 'buy-volume', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
      { i: 'sell-volume', x: 6, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
      { i: 'imbalance', x: 9, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
      
      // Charts in main area
      { i: 'price-timeline', x: 0, y: 2, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'supply-demand', x: 6, y: 2, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'volume-analysis', x: 0, y: 10, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'constraints', x: 6, y: 10, w: 6, h: 8, minW: 4, minH: 6 },
      
      // Table spans full width
      { i: 'participants', x: 0, y: 18, w: 12, h: 6, minW: 8, minH: 4 },
      
      // Allocation flow (when available)
      { i: 'allocations', x: 0, y: 24, w: 12, h: 8, minW: 8, minH: 6 },
    ],
    md: [
      { i: 'price', x: 0, y: 0, w: 2, h: 2 },
      { i: 'buy-volume', x: 2, y: 0, w: 2, h: 2 },
      { i: 'sell-volume', x: 4, y: 0, w: 2, h: 2 },
      { i: 'imbalance', x: 6, y: 0, w: 2, h: 2 },
      { i: 'price-timeline', x: 0, y: 2, w: 4, h: 8 },
      { i: 'supply-demand', x: 4, y: 2, w: 4, h: 8 },
      { i: 'volume-analysis', x: 0, y: 10, w: 4, h: 8 },
      { i: 'constraints', x: 4, y: 10, w: 4, h: 8 },
      { i: 'participants', x: 0, y: 18, w: 8, h: 6 },
      { i: 'allocations', x: 0, y: 24, w: 8, h: 8 },
    ],
    sm: [
      { i: 'price', x: 0, y: 0, w: 2, h: 2 },
      { i: 'buy-volume', x: 2, y: 0, w: 2, h: 2 },
      { i: 'sell-volume', x: 0, y: 2, w: 2, h: 2 },
      { i: 'imbalance', x: 2, y: 2, w: 2, h: 2 },
      { i: 'price-timeline', x: 0, y: 4, w: 4, h: 8 },
      { i: 'supply-demand', x: 0, y: 12, w: 4, h: 8 },
      { i: 'volume-analysis', x: 0, y: 20, w: 4, h: 8 },
      { i: 'constraints', x: 0, y: 28, w: 4, h: 8 },
      { i: 'participants', x: 0, y: 36, w: 4, h: 6 },
      { i: 'allocations', x: 0, y: 42, w: 4, h: 8 },
    ]
  };

  const [layouts, setLayouts] = useState(defaultLayouts);

  // Handle layout changes
  const handleLayoutChange = (layout: Layout[], allLayouts: any) => {
    setLayouts(allLayouts);
  };

  // Status card component
  const StatusCard = ({ title, value, subtitle, color }: {
    title: string;
    value: string | number;
    subtitle: string;
    color: string;
  }) => (
    <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col justify-center">
      <h3 className="text-sm font-medium text-gray-500 mb-1">{title}</h3>
      <div className={`text-2xl font-bold ${color}`}>
        {value}
      </div>
      <div className="text-xs text-gray-400 mt-1">
        {subtitle}
      </div>
    </div>
  );

  // Chart wrapper component
  const ChartCard = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <div className="flex-1 min-h-0">
        {children}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={handleLayoutChange}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 8, sm: 4, xs: 2, xxs: 1 }}
        rowHeight={30}
        isDraggable={true}
        isResizable={true}
        margin={[16, 16]}
        containerPadding={[16, 16]}
      >
        {/* Status Cards */}
        <div key="price">
          <StatusCard
            title="Current Price"
            value={`$${latestRound.announcedPrice}`}
            subtitle={`Round ${latestRound.roundNumber}`}
            color="text-blue-600"
          />
        </div>

        <div key="buy-volume">
          <StatusCard
            title="Buy Volume"
            value={latestRound.totalBuyVolume}
            subtitle="Total demand"
            color="text-green-600"
          />
        </div>

        <div key="sell-volume">
          <StatusCard
            title="Sell Volume"
            value={latestRound.totalSellVolume}
            subtitle="Total supply"
            color="text-red-600"
          />
        </div>

        <div key="imbalance">
          <StatusCard
            title="Imbalance"
            value={`${latestRound.totalBuyVolume - latestRound.totalSellVolume > 0 ? '+' : ''}${latestRound.totalBuyVolume - latestRound.totalSellVolume}`}
            subtitle="Buy - Sell"
            color={
              latestRound.totalBuyVolume - latestRound.totalSellVolume > 0
                ? 'text-green-600'
                : latestRound.totalBuyVolume - latestRound.totalSellVolume < 0
                  ? 'text-red-600'
                  : 'text-gray-600'
            }
          />
        </div>

        {/* Charts */}
        <div key="price-timeline">
          <ChartCard title="📈 Price Discovery Timeline">
            <PriceTimelineChart rounds={rounds} height={200} showVolumes={true} />
          </ChartCard>
        </div>

        <div key="supply-demand">
          <ChartCard title="📊 Supply & Demand Curves">
            <SupplyDemandChart
              participants={participants}
              rounds={rounds}
              currentPrice={currentPrice}
              height={200}
            />
          </ChartCard>
        </div>

        <div key="volume-analysis">
          <ChartCard title="📊 Volume Analysis">
            <VolumeAnalysisChart
              rounds={rounds}
              participants={participants}
              height={200}
              chartType="stacked"
            />
          </ChartCard>
        </div>

        <div key="constraints">
          <ChartCard title="🔥 Constraint Tracking">
            <ConstraintHeatmap
              rounds={rounds}
              participants={participants}
              height={200}
            />
          </ChartCard>
        </div>

        {/* Participants Table */}
        <div key="participants">
          <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
            <div className="flex-1 min-h-0">
              <ParticipantsTable
                participants={participants}
                rounds={rounds}
                currentPrice={currentPrice}
              />
            </div>
          </div>
        </div>

        {/* Allocation Flow (only when auction is complete) */}
        {auctionResult && (
          <div key="allocations">
            <ChartCard title="🔄 Final Allocations">
              <AllocationFlowChart
                auctionResult={auctionResult}
                participants={participants}
                height={200}
              />
            </ChartCard>
          </div>
        )}
      </ResponsiveGridLayout>
    </div>
  );
}
