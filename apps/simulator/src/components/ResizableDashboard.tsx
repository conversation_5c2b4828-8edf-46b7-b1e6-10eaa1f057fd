import React, { useState, useMemo } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { PriceTimelineChart } from './charts/PriceTimelineChart';
import { PriceTimelineStepChart } from './charts/PriceTimelineStepChart';
import { SupplyDemandChart } from './charts/SupplyDemandChart';
import { VolumeAnalysisChart } from './charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from './charts/ConstraintHeatmap';
import { AllocationFlowChart } from './charts/AllocationFlowChart';
import { SankeyAllocationFlow } from './charts/SankeyAllocationFlow';
import { ParticipantsTable } from './ParticipantsTable';
import { SimulatorRoundTable } from './tables/SimulatorRoundTable';
import { OrdersTable } from './tables/OrdersTable';
import { QuantityConstraintsWidget } from './widgets/QuantityConstraintsWidget';
import { RibbonChart3D } from './charts/RibbonChart3D';
import type { ClockAuctionResult, ClockAuctionRound, ClockParticipant } from '../types/types';

// Make ResponsiveGridLayout responsive to container width
const ResponsiveGridLayout = WidthProvider(Responsive);

interface ResizableDashboardProps {
  rounds: ClockAuctionRound[];
  auctionResult: ClockAuctionResult | null;
  currentRound: number;
  participants: ClockParticipant[];
}

export function ResizableDashboard({
  rounds,
  auctionResult,
  participants
}: ResizableDashboardProps) {

  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <div className="text-gray-500 text-lg mb-2">
            🎯 Ready to start auction simulation
          </div>
          <p className="text-gray-400">
            Click "Start New Auction" to begin the double-sided clock auction
          </p>
        </div>
      </div>
    );
  }

  const latestRound = rounds[rounds.length - 1];
  const currentPrice = latestRound.announcedPrice;

  // Define the default layout for all widgets
  const defaultLayouts = {
    lg: [
      // Status cards in top row - more compact height
      { i: 'price', x: 0, y: 0, w: 3, h: 1.5, minW: 2, minH: 1 },
      { i: 'buy-volume', x: 3, y: 0, w: 3, h: 1.5, minW: 2, minH: 1 },
      { i: 'sell-volume', x: 6, y: 0, w: 3, h: 1.5, minW: 2, minH: 1 },
      { i: 'imbalance', x: 9, y: 0, w: 3, h: 1.5, minW: 2, minH: 1 },
      
      // Charts in main area
      { i: 'price-timeline', x: 0, y: 2, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'price-step-chart', x: 6, y: 2, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'supply-demand', x: 0, y: 10, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'volume-analysis', x: 6, y: 10, w: 6, h: 8, minW: 4, minH: 6 },
      { i: 'constraints', x: 0, y: 18, w: 6, h: 8, minW: 4, minH: 6 },
      
      // Tables span full width
      { i: 'participants', x: 0, y: 26, w: 6, h: 6, minW: 4, minH: 4 },
      { i: 'orders-table', x: 6, y: 26, w: 6, h: 6, minW: 4, minH: 4 },
      { i: 'round-table', x: 0, y: 32, w: 12, h: 8, minW: 8, minH: 6 },
      { i: 'quantity-constraints', x: 0, y: 40, w: 12, h: 8, minW: 8, minH: 6 },
      { i: '3d-ribbon', x: 0, y: 48, w: 12, h: 10, minW: 8, minH: 8 },

      // Allocation flow (when available)
      { i: 'allocations', x: 0, y: 58, w: 12, h: 8, minW: 8, minH: 6 },
    ],
    md: [
      { i: 'price', x: 0, y: 0, w: 2, h: 2 },
      { i: 'buy-volume', x: 2, y: 0, w: 2, h: 2 },
      { i: 'sell-volume', x: 4, y: 0, w: 2, h: 2 },
      { i: 'imbalance', x: 6, y: 0, w: 2, h: 2 },
      { i: 'price-timeline', x: 0, y: 2, w: 4, h: 8 },
      { i: 'price-step-chart', x: 4, y: 2, w: 4, h: 8 },
      { i: 'supply-demand', x: 0, y: 10, w: 4, h: 8 },
      { i: 'volume-analysis', x: 4, y: 10, w: 4, h: 8 },
      { i: 'constraints', x: 0, y: 18, w: 4, h: 8 },
      { i: 'participants', x: 0, y: 26, w: 4, h: 6 },
      { i: 'orders-table', x: 4, y: 26, w: 4, h: 6 },
      { i: 'round-table', x: 0, y: 32, w: 8, h: 8 },
      { i: 'quantity-constraints', x: 0, y: 40, w: 8, h: 8 },
      { i: '3d-ribbon', x: 0, y: 48, w: 8, h: 10 },
      { i: 'allocations', x: 0, y: 58, w: 8, h: 8 },
    ],
    sm: [
      { i: 'price', x: 0, y: 0, w: 2, h: 2 },
      { i: 'buy-volume', x: 2, y: 0, w: 2, h: 2 },
      { i: 'sell-volume', x: 0, y: 2, w: 2, h: 2 },
      { i: 'imbalance', x: 2, y: 2, w: 2, h: 2 },
      { i: 'price-timeline', x: 0, y: 4, w: 4, h: 8 },
      { i: 'price-step-chart', x: 0, y: 12, w: 4, h: 8 },
      { i: 'supply-demand', x: 0, y: 20, w: 4, h: 8 },
      { i: 'volume-analysis', x: 0, y: 28, w: 4, h: 8 },
      { i: 'constraints', x: 0, y: 36, w: 4, h: 8 },
      { i: 'participants', x: 0, y: 44, w: 4, h: 6 },
      { i: 'orders-table', x: 0, y: 50, w: 4, h: 6 },
      { i: 'round-table', x: 0, y: 56, w: 4, h: 8 },
      { i: 'quantity-constraints', x: 0, y: 64, w: 4, h: 8 },
      { i: '3d-ribbon', x: 0, y: 72, w: 4, h: 10 },
      { i: 'allocations', x: 0, y: 82, w: 4, h: 8 },
    ]
  };

  const [layouts, setLayouts] = useState(defaultLayouts);

  // Handle layout changes
  const handleLayoutChange = (layout: Layout[], allLayouts: any) => {
    setLayouts(allLayouts);
  };

  // Calculate change indicators from previous round
  const getChangeIndicator = (currentValue: number, previousValue: number | null): { direction: 'up' | 'down'; value: number; percentage: number } | null => {
    if (previousValue === null) return null;
    const change = currentValue - previousValue;
    if (Math.abs(change) < 0.01) return null;

    return {
      direction: change > 0 ? 'up' as const : 'down' as const,
      value: Math.abs(change),
      percentage: previousValue !== 0 ? Math.abs(change / previousValue) * 100 : 0
    };
  };

  const previousRound = rounds.length > 1 ? rounds[rounds.length - 2] : null;

  // Status card component with change indicators
  const StatusCard = ({ title, value, subtitle, color, change }: {
    title: string;
    value: string | number;
    subtitle: string;
    color: string;
    change?: { direction: 'up' | 'down'; value: number; percentage: number } | null;
  }) => (
    <div className="bg-white rounded-lg shadow-md p-3 h-full flex items-center">
      <div className="flex-1">
        <h3 className="text-xs font-medium text-gray-500 mb-1">{title}</h3>
        <div className={`text-xl font-bold ${color} flex items-center`}>
          {value}
          {change && (
            <span className={`ml-2 text-sm flex items-center ${
              change.direction === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {change.direction === 'up' ? '↗' : '↘'}
              <span className="ml-1 text-xs">
                {typeof value === 'string' && value.startsWith('$')
                  ? `$${change.value.toFixed(2)}`
                  : change.value.toFixed(0)
                }
              </span>
            </span>
          )}
        </div>
        <div className="text-xs text-gray-400">
          {subtitle}
        </div>
      </div>
    </div>
  );

  // Chart wrapper component
  const ChartCard = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <div className="flex-1 min-h-0">
        {children}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={handleLayoutChange}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 8, sm: 4, xs: 2, xxs: 1 }}
        rowHeight={30}
        isDraggable={true}
        isResizable={true}
        margin={[16, 16]}
        containerPadding={[16, 16]}
      >
        {/* Status Cards */}
        <div key="price">
          <StatusCard
            title="Current Price"
            value={`$${latestRound.announcedPrice}`}
            subtitle={`Round ${latestRound.roundNumber}`}
            color="text-blue-600"
            change={previousRound ? getChangeIndicator(latestRound.announcedPrice, previousRound.announcedPrice) : null}
          />
        </div>

        <div key="buy-volume">
          <StatusCard
            title="Buy Volume"
            value={latestRound.totalBuyVolume}
            subtitle="Total demand"
            color="text-green-600"
            change={previousRound ? getChangeIndicator(latestRound.totalBuyVolume, previousRound.totalBuyVolume) : null}
          />
        </div>

        <div key="sell-volume">
          <StatusCard
            title="Sell Volume"
            value={latestRound.totalSellVolume}
            subtitle="Total supply"
            color="text-red-600"
            change={previousRound ? getChangeIndicator(latestRound.totalSellVolume, previousRound.totalSellVolume) : null}
          />
        </div>

        <div key="imbalance">
          <StatusCard
            title="Imbalance"
            value={`${latestRound.totalBuyVolume - latestRound.totalSellVolume > 0 ? '+' : ''}${latestRound.totalBuyVolume - latestRound.totalSellVolume}`}
            subtitle="Buy - Sell"
            color={
              latestRound.totalBuyVolume - latestRound.totalSellVolume > 0
                ? 'text-green-600'
                : latestRound.totalBuyVolume - latestRound.totalSellVolume < 0
                  ? 'text-red-600'
                  : 'text-gray-600'
            }
            change={previousRound ? getChangeIndicator(
              latestRound.totalBuyVolume - latestRound.totalSellVolume,
              previousRound.totalBuyVolume - previousRound.totalSellVolume
            ) : null}
          />
        </div>

        {/* Charts */}
        <div key="price-timeline">
          <ChartCard title="📈 Price Discovery Timeline">
            <PriceTimelineChart rounds={rounds} height={200} showVolumes={true} />
          </ChartCard>
        </div>

        <div key="price-step-chart">
          <ChartCard title="📊 Price × Quantity Step Chart">
            <PriceTimelineStepChart rounds={rounds} height={200} showRoundOverlay={true} />
          </ChartCard>
        </div>

        <div key="supply-demand">
          <ChartCard title="📊 Supply & Demand Curves">
            <SupplyDemandChart
              participants={participants}
              rounds={rounds}
              currentPrice={currentPrice}
              height={200}
            />
          </ChartCard>
        </div>

        <div key="volume-analysis">
          <ChartCard title="📊 Volume Analysis">
            <VolumeAnalysisChart
              rounds={rounds}
              participants={participants}
              height={300}
              chartType="stacked"
            />
          </ChartCard>
        </div>

        <div key="constraints">
          <ChartCard title="🔥 Constraint Tracking">
            <ConstraintHeatmap
              rounds={rounds}
              participants={participants}
              height={200}
            />
          </ChartCard>
        </div>

        {/* Participants Table */}
        <div key="participants">
          <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
            <div className="flex-1 min-h-0">
              <ParticipantsTable
                participants={participants}
                rounds={rounds}
                currentPrice={currentPrice}
              />
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div key="orders-table">
          <ChartCard title="📋 Orders by Round">
            <OrdersTable
              rounds={rounds}
              participants={participants}
              height={250}
              initialRound={rounds.length}
            />
          </ChartCard>
        </div>

        {/* Round Table */}
        <div key="round-table">
          <ChartCard title="📊 Participant Bids by Round">
            <SimulatorRoundTable
              rounds={rounds}
              participants={participants}
              height={300}
              selectedRound={rounds.length}
              onSelectedRoundChange={(round) => console.log('Selected round:', round)}
              onParticipantClick={(participantId) => console.log('Participant clicked:', participantId)}
            />
          </ChartCard>
        </div>

        {/* Quantity Constraints Widget */}
        <div key="quantity-constraints">
          <ChartCard title="📊 Quantity Constraints (Patented)">
            <QuantityConstraintsWidget
              rounds={rounds}
              participants={participants}
              height={300}
              onParticipantSelect={(participantId) => console.log('Participant selected:', participantId)}
            />
          </ChartCard>
        </div>

        {/* 3D Ribbon Chart */}
        <div key="3d-ribbon">
          <ChartCard title="🎗️ 3D Trader Demand/Supply Ribbons">
            <RibbonChart3D
              rounds={rounds}
              participants={participants}
              width={800}
              height={400}
              showBuyRibbons={true}
              showSellRibbons={true}
            />
          </ChartCard>
        </div>

        {/* Allocation Flow (only when auction is complete) */}
        {auctionResult && (
          <div key="allocations">
            <ChartCard title="🔄 Final Allocations">
              <AllocationFlowChart
                auctionResult={auctionResult}
                participants={participants}
                height={300}
              />
            </ChartCard>
          </div>
        )}

        {/* Show Sankey flow during auction (before completion) */}
        {!auctionResult && rounds.length > 0 && (
          <div key="allocations">
            <ChartCard title="🔄 Current Allocation Flow">
              <SankeyAllocationFlow
                auctionResult={null}
                participants={participants}
                rounds={rounds}
                height={300}
              />
            </ChartCard>
          </div>
        )}
      </ResponsiveGridLayout>
    </div>
  );
}
