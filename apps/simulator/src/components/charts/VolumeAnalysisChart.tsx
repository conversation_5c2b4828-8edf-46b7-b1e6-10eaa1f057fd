import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
  <PERSON><PERSON><PERSON>,
  Pie,
  Treemap
} from 'recharts';
import type { ClockAuctionRound, ClockParticipant } from '../../types/types';

interface VolumeAnalysisChartProps {
  rounds: ClockAuctionRound[];
  participants: ClockParticipant[];
  width?: number;
  height?: number;
  chartType?: 'stacked' | 'grouped' | 'pie' | 'treemap';
}

interface VolumeData {
  round: number;
  price: number;
  totalBuy: number;
  totalSell: number;
  imbalance: number;
  participants: ParticipantVolume[];
}

interface ParticipantVolume {
  participantId: string;
  participantName: string;
  buyVolume: number;
  sellVolume: number;
  netVolume: number;
  color: string;
}

const PARTICIPANT_COLORS = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
  '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
];

export function VolumeAnalysisChart({ 
  rounds, 
  participants,
  width = 800, 
  height = 400,
  chartType = 'stacked'
}: VolumeAnalysisChartProps) {
  
  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📊</div>
          <div className="text-gray-500">Volume analysis will appear here</div>
        </div>
      </div>
    );
  }

  // Transform data for visualization
  const volumeData: VolumeData[] = rounds.map(round => {
    const participantVolumes: ParticipantVolume[] = participants.map((participant, index) => {
      const participantBids = round.bids.filter(bid => bid.participantId === participant.id);
      const buyVolume = participantBids
        .filter(bid => bid.side === 'buy')
        .reduce((sum, bid) => sum + bid.quantity, 0);
      const sellVolume = participantBids
        .filter(bid => bid.side === 'sell')
        .reduce((sum, bid) => sum + bid.quantity, 0);

      return {
        participantId: participant.id,
        participantName: participant.name,
        buyVolume,
        sellVolume,
        netVolume: buyVolume - sellVolume,
        color: PARTICIPANT_COLORS[index % PARTICIPANT_COLORS.length]
      };
    });

    return {
      round: round.roundNumber,
      price: round.announcedPrice,
      totalBuy: round.totalBuyVolume,
      totalSell: round.totalSellVolume,
      imbalance: round.totalBuyVolume - round.totalSellVolume,
      participants: participantVolumes
    };
  });

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = volumeData.find(d => d.round === label);
      if (!data) return null;

      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg max-w-xs">
          <p className="font-semibold text-gray-900 mb-2">Round {label}</p>
          <p className="text-sm text-gray-600 mb-2">Price: ${data.price}</p>
          
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span className="text-green-600">Total Buy:</span>
              <span className="font-medium">{data.totalBuy}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-red-600">Total Sell:</span>
              <span className="font-medium">{data.totalSell}</span>
            </div>
            <div className="flex justify-between text-sm border-t pt-1">
              <span className={data.imbalance > 0 ? 'text-green-600' : data.imbalance < 0 ? 'text-red-600' : 'text-gray-600'}>
                Imbalance:
              </span>
              <span className="font-medium">
                {data.imbalance > 0 ? '+' : ''}{data.imbalance}
              </span>
            </div>
          </div>

          {/* Top participants */}
          <div className="mt-3 pt-2 border-t border-gray-100">
            <p className="text-xs text-gray-500 mb-1">Active Participants:</p>
            {data.participants
              .filter(p => p.buyVolume > 0 || p.sellVolume > 0)
              .slice(0, 3)
              .map(p => (
                <div key={p.participantId} className="text-xs flex justify-between">
                  <span className="truncate mr-2">{p.participantName.split(' ')[0]}</span>
                  <span>
                    {p.buyVolume > 0 && <span className="text-green-600">B:{p.buyVolume}</span>}
                    {p.buyVolume > 0 && p.sellVolume > 0 && <span className="text-gray-400"> | </span>}
                    {p.sellVolume > 0 && <span className="text-red-600">S:{p.sellVolume}</span>}
                  </span>
                </div>
              ))}
          </div>
        </div>
      );
    }
    return null;
  };

  if (chartType === 'pie') {
    // Pie chart showing total volume by participant
    const latestRound = volumeData[volumeData.length - 1];
    const pieData = latestRound.participants
      .filter(p => p.buyVolume > 0 || p.sellVolume > 0)
      .map(p => ({
        name: p.participantName,
        value: p.buyVolume + p.sellVolume,
        fill: p.color
      }));

    return (
      <div className="w-full">
        <h4 className="text-center text-sm text-gray-600 mb-4">
          Volume Distribution - Round {latestRound.round}
        </h4>
        <ResponsiveContainer width="100%" height={height}>
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              outerRadius={Math.min(width, height) / 4}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  }

  if (chartType === 'treemap') {
    // Treemap showing participant activity
    const latestRound = volumeData[volumeData.length - 1];
    const treemapData = latestRound.participants
      .filter(p => p.buyVolume > 0 || p.sellVolume > 0)
      .map(p => ({
        name: p.participantName,
        size: p.buyVolume + p.sellVolume,
        buyVolume: p.buyVolume,
        sellVolume: p.sellVolume,
        fill: p.color
      }));

    return (
      <div className="w-full">
        <h4 className="text-center text-sm text-gray-600 mb-4">
          Participant Activity - Round {latestRound.round}
        </h4>
        <ResponsiveContainer width="100%" height={height}>
          <Treemap
            data={treemapData}
            dataKey="size"
            aspectRatio={4/3}
            stroke="#fff"
            fill="#8884d8"
          />
        </ResponsiveContainer>
      </div>
    );
  }

  // Prepare data for stacked/grouped bar chart
  const chartData = volumeData.map(data => {
    const result: any = {
      round: data.round,
      price: data.price,
      totalBuy: data.totalBuy,
      totalSell: data.totalSell,
      imbalance: data.imbalance
    };

    // Add individual participant data
    data.participants.forEach(p => {
      result[`${p.participantName}_buy`] = p.buyVolume;
      result[`${p.participantName}_sell`] = p.sellVolume;
    });

    return result;
  });

  if (chartType === 'grouped') {
    return (
      <div className="w-full">
        <ResponsiveContainer width="100%" height={height}>
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="round" 
              stroke="#666"
              fontSize={12}
              tickFormatter={(value) => `R${value}`}
            />
            <YAxis stroke="#666" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="totalBuy" fill="#10b981" name="Total Buy" />
            <Bar dataKey="totalSell" fill="#ef4444" name="Total Sell" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  // Default: Stacked bar chart with participant breakdown
  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="round" 
            stroke="#666"
            fontSize={12}
            tickFormatter={(value) => `R${value}`}
          />
          <YAxis stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* Buy volumes by participant */}
          {participants.map((participant, index) => (
            <Bar
              key={`${participant.id}_buy`}
              dataKey={`${participant.name}_buy`}
              stackId="buy"
              fill={PARTICIPANT_COLORS[index % PARTICIPANT_COLORS.length]}
              fillOpacity={0.8}
              name={`${participant.name} (Buy)`}
            />
          ))}
          
          {/* Sell volumes by participant (negative values) */}
          {participants.map((participant, index) => (
            <Bar
              key={`${participant.id}_sell`}
              dataKey={`${participant.name}_sell`}
              stackId="sell"
              fill={PARTICIPANT_COLORS[index % PARTICIPANT_COLORS.length]}
              fillOpacity={0.4}
              name={`${participant.name} (Sell)`}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
