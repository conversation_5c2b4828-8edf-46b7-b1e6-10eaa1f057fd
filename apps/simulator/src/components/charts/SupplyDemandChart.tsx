import React, { useMemo } from 'react';
import { Group } from '@visx/group';
import { scaleLinear } from '@visx/scale';
import { LinePath, Line, Circle } from '@visx/shape';
import { AxisLeft, AxisBottom } from '@visx/axis';
import { GridRows, GridColumns } from '@visx/grid';
import { curveLinear } from '@visx/curve';
import type { ClockParticipant, ClockAuctionRound } from '../../types/types';

interface SupplyDemandChartProps {
  participants: ClockParticipant[];
  rounds: ClockAuctionRound[];
  currentPrice?: number;
  width?: number;
  height?: number;
}

interface CurvePoint {
  price: number;
  quantity: number;
  participantId?: string;
  side: 'buy' | 'sell';
}

interface AggregateCurvePoint {
  price: number;
  totalBuyQuantity: number;
  totalSellQuantity: number;
}

const margin = { top: 40, right: 40, bottom: 60, left: 60 };

export function SupplyDemandChart({ 
  participants, 
  rounds,
  currentPrice,
  width = 600, 
  height = 400 
}: SupplyDemandChartProps) {
  
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  const { aggregateCurve, individualCurves, priceRange, quantityRange, equilibriumPoint } = useMemo(() => {
    if (participants.length === 0) {
      return { 
        aggregateCurve: [], 
        individualCurves: [], 
        priceRange: [0, 100], 
        quantityRange: [-50, 50],
        equilibriumPoint: null 
      };
    }

    // Generate price points for curve calculation
    const minPrice = Math.min(...participants.map(p => p.curveParams.zeroPrice)) - 20;
    const maxPrice = Math.max(...participants.map(p => p.curveParams.zeroPrice)) + 20;
    const priceStep = (maxPrice - minPrice) / 50;
    const pricePoints = Array.from({ length: 51 }, (_, i) => minPrice + i * priceStep);

    // Calculate individual participant curves
    const individualCurves = participants.map(participant => {
      const points = pricePoints.map(price => {
        const quantity = participant.quantityFunction(price);
        return {
          price,
          quantity,
          participantId: participant.id,
          side: quantity > 0 ? 'sell' as const : 'buy' as const
        };
      });
      return {
        participantId: participant.id,
        name: participant.name,
        points,
        color: getParticipantColor(participant.id)
      };
    });

    // Calculate aggregate supply and demand curves
    const aggregateCurve: AggregateCurvePoint[] = pricePoints.map(price => {
      let totalBuyQuantity = 0;
      let totalSellQuantity = 0;

      participants.forEach(participant => {
        const quantity = participant.quantityFunction(price);
        if (quantity > 0) {
          totalSellQuantity += quantity;
        } else {
          totalBuyQuantity += Math.abs(quantity);
        }
      });

      return {
        price,
        totalBuyQuantity,
        totalSellQuantity
      };
    });

    // Find equilibrium point (where supply meets demand)
    let equilibriumPoint = null;
    for (let i = 0; i < aggregateCurve.length - 1; i++) {
      const current = aggregateCurve[i];
      const next = aggregateCurve[i + 1];
      
      // Check if supply and demand curves cross
      const currentDiff = current.totalSellQuantity - current.totalBuyQuantity;
      const nextDiff = next.totalSellQuantity - next.totalBuyQuantity;
      
      if (currentDiff * nextDiff <= 0) {
        // Linear interpolation to find exact crossing point
        const ratio = Math.abs(currentDiff) / (Math.abs(currentDiff) + Math.abs(nextDiff));
        equilibriumPoint = {
          price: current.price + ratio * (next.price - current.price),
          quantity: current.totalBuyQuantity + ratio * (next.totalBuyQuantity - current.totalBuyQuantity)
        };
        break;
      }
    }

    const allQuantities = aggregateCurve.flatMap(d => [d.totalBuyQuantity, -d.totalSellQuantity]);
    const maxQuantity = Math.max(...allQuantities.map(Math.abs));
    
    return {
      aggregateCurve,
      individualCurves,
      priceRange: [minPrice, maxPrice],
      quantityRange: [-maxQuantity * 1.1, maxQuantity * 1.1],
      equilibriumPoint
    };
  }, [participants]);

  // Scales
  const xScale = scaleLinear({
    range: [0, innerWidth],
    domain: quantityRange,
  });

  const yScale = scaleLinear({
    range: [innerHeight, 0],
    domain: priceRange,
  });

  if (participants.length === 0) {
    return (
      <div className="flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300" style={{ width, height }}>
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📊</div>
          <div className="text-gray-500">Supply & demand curves will appear here</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <svg width={width} height={height}>
        <Group left={margin.left} top={margin.top}>
          {/* Grid */}
          <GridRows
            scale={yScale}
            width={innerWidth}
            stroke="#f0f0f0"
            strokeDasharray="2,2"
          />
          <GridColumns
            scale={xScale}
            height={innerHeight}
            stroke="#f0f0f0"
            strokeDasharray="2,2"
          />

          {/* Zero line */}
          <Line
            from={{ x: xScale(0), y: 0 }}
            to={{ x: xScale(0), y: innerHeight }}
            stroke="#d1d5db"
            strokeWidth={2}
          />

          {/* Individual participant curves (faded) */}
          {individualCurves.map(curve => (
            <LinePath
              key={curve.participantId}
              data={curve.points}
              x={d => xScale(d.quantity)}
              y={d => yScale(d.price)}
              stroke={curve.color}
              strokeWidth={1}
              strokeOpacity={0.3}
              curve={curveLinear}
            />
          ))}

          {/* Aggregate demand curve (buy side - left side, negative quantities) */}
          <LinePath
            data={aggregateCurve}
            x={d => xScale(-d.totalBuyQuantity)}
            y={d => yScale(d.price)}
            stroke="#10b981"
            strokeWidth={3}
            curve={curveLinear}
          />

          {/* Aggregate supply curve (sell side - right side, positive quantities) */}
          <LinePath
            data={aggregateCurve}
            x={d => xScale(d.totalSellQuantity)}
            y={d => yScale(d.price)}
            stroke="#ef4444"
            strokeWidth={3}
            curve={curveLinear}
          />

          {/* Current price line */}
          {currentPrice && (
            <Line
              from={{ x: 0, y: yScale(currentPrice) }}
              to={{ x: innerWidth, y: yScale(currentPrice) }}
              stroke="#2563eb"
              strokeWidth={2}
              strokeDasharray="5,5"
            />
          )}

          {/* Equilibrium point */}
          {equilibriumPoint && (
            <Circle
              cx={xScale(0)}
              cy={yScale(equilibriumPoint.price)}
              r={6}
              fill="#f59e0b"
              stroke="#ffffff"
              strokeWidth={2}
            />
          )}

          {/* Axes */}
          <AxisLeft
            scale={yScale}
            stroke="#666"
            tickStroke="#666"
            tickLabelProps={{
              fill: '#666',
              fontSize: 12,
              textAnchor: 'end',
              dy: '0.33em',
            }}
            tickFormat={(value) => `$${value}`}
          />
          <AxisBottom
            top={innerHeight}
            scale={xScale}
            stroke="#666"
            tickStroke="#666"
            tickLabelProps={{
              fill: '#666',
              fontSize: 12,
              textAnchor: 'middle',
            }}
          />
        </Group>

        {/* Labels */}
        <text
          x={margin.left + innerWidth / 2}
          y={height - 20}
          textAnchor="middle"
          fontSize={14}
          fill="#666"
        >
          Quantity (Buy ← | → Sell)
        </text>
        <text
          x={20}
          y={margin.top + innerHeight / 2}
          textAnchor="middle"
          fontSize={14}
          fill="#666"
          transform={`rotate(-90, 20, ${margin.top + innerHeight / 2})`}
        >
          Price ($)
        </text>

        {/* Current price label */}
        {currentPrice && (
          <text
            x={margin.left + innerWidth + 5}
            y={margin.top + yScale(currentPrice) + 4}
            fontSize={12}
            fill="#2563eb"
            fontWeight="bold"
          >
            ${currentPrice}
          </text>
        )}

        {/* Equilibrium label */}
        {equilibriumPoint && (
          <text
            x={margin.left + xScale(0) + 10}
            y={margin.top + yScale(equilibriumPoint.price) - 10}
            fontSize={12}
            fill="#f59e0b"
            fontWeight="bold"
          >
            Equilibrium: ${equilibriumPoint.price.toFixed(2)}
          </text>
        )}
      </svg>

      {/* Legend */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 p-3 rounded-lg border border-gray-200 text-sm">
        <div className="flex items-center mb-1">
          <div className="w-4 h-0.5 bg-green-500 mr-2"></div>
          <span>Demand (Buy)</span>
        </div>
        <div className="flex items-center mb-1">
          <div className="w-4 h-0.5 bg-red-500 mr-2"></div>
          <span>Supply (Sell)</span>
        </div>
        {currentPrice && (
          <div className="flex items-center mb-1">
            <div className="w-4 h-0.5 bg-blue-500 border-dashed mr-2"></div>
            <span>Current Price</span>
          </div>
        )}
        {equilibriumPoint && (
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
            <span>Equilibrium</span>
          </div>
        )}
      </div>
    </div>
  );
}

function getParticipantColor(participantId: string): string {
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
  ];
  const hash = participantId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
}
