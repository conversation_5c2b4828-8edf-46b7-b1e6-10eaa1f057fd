import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  Area,
  ComposedChart,
  Bar
} from 'recharts';
import type { ClockAuctionRound } from '../../types/types';

interface PriceTimelineChartProps {
  rounds: ClockAuctionRound[];
  width?: number;
  height?: number;
  showVolumes?: boolean;
}

interface ChartDataPoint {
  round: number;
  price: number;
  buyVolume: number;
  sellVolume: number;
  imbalance: number;
  priceChange: number;
  isReversal?: boolean;
}

export function PriceTimelineChart({ 
  rounds, 
  width = 800, 
  height = 400,
  showVolumes = true 
}: PriceTimelineChartProps) {
  
  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📈</div>
          <div className="text-gray-500">Price timeline will appear here</div>
        </div>
      </div>
    );
  }

  // Transform rounds data for charting
  const chartData: ChartDataPoint[] = rounds.map((round, index) => {
    const prevRound = index > 0 ? rounds[index - 1] : null;
    const priceChange = prevRound ? round.announcedPrice - prevRound.announcedPrice : 0;
    
    // Detect price reversal
    const isReversal = index > 1 && prevRound && rounds[index - 2] ? 
      (rounds[index - 2].announcedPrice < prevRound.announcedPrice && round.announcedPrice < prevRound.announcedPrice) ||
      (rounds[index - 2].announcedPrice > prevRound.announcedPrice && round.announcedPrice > prevRound.announcedPrice)
      : false;

    return {
      round: round.roundNumber,
      price: round.announcedPrice,
      buyVolume: round.totalBuyVolume,
      sellVolume: round.totalSellVolume,
      imbalance: round.totalBuyVolume - round.totalSellVolume,
      priceChange,
      isReversal
    };
  });

  const maxPrice = Math.max(...chartData.map(d => d.price));
  const minPrice = Math.min(...chartData.map(d => d.price));
  const priceRange = maxPrice - minPrice;
  const pricePadding = priceRange * 0.1;

  const maxVolume = Math.max(...chartData.flatMap(d => [d.buyVolume, d.sellVolume]));

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">Round {label}</p>
          <p className="text-blue-600">
            Price: <span className="font-medium">${data.price}</span>
          </p>
          {data.priceChange !== 0 && (
            <p className={`text-sm ${data.priceChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
              Change: {data.priceChange > 0 ? '+' : ''}${data.priceChange.toFixed(2)}
            </p>
          )}
          <div className="mt-2 pt-2 border-t border-gray-100">
            <p className="text-green-600 text-sm">
              Buy Volume: <span className="font-medium">{data.buyVolume}</span>
            </p>
            <p className="text-red-600 text-sm">
              Sell Volume: <span className="font-medium">{data.sellVolume}</span>
            </p>
            <p className={`text-sm font-medium ${data.imbalance > 0 ? 'text-green-600' : data.imbalance < 0 ? 'text-red-600' : 'text-gray-600'}`}>
              Imbalance: {data.imbalance > 0 ? '+' : ''}{data.imbalance}
            </p>
          </div>
          {data.isReversal && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <p className="text-purple-600 text-sm font-medium">🔄 Price Reversal</p>
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  // Custom dot for price reversals
  const CustomDot = (props: any) => {
    const { cx, cy, payload } = props;
    if (payload.isReversal) {
      return (
        <circle 
          cx={cx} 
          cy={cy} 
          r={6} 
          fill="#8b5cf6" 
          stroke="#ffffff" 
          strokeWidth={2}
        />
      );
    }
    return null;
  };

  if (!showVolumes) {
    // Simple price line chart
    return (
      <div className="w-full">
        <ResponsiveContainer width="100%" height={height}>
          <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="round" 
              stroke="#666"
              fontSize={12}
              tickFormatter={(value) => `R${value}`}
            />
            <YAxis 
              stroke="#666"
              fontSize={12}
              domain={[minPrice - pricePadding, maxPrice + pricePadding]}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="price" 
              stroke="#2563eb" 
              strokeWidth={3}
              dot={<CustomDot />}
              activeDot={{ r: 6, fill: '#2563eb' }}
              name="Price"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }

  // Combined chart with price and volumes
  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="round" 
            stroke="#666"
            fontSize={12}
            tickFormatter={(value) => `R${value}`}
          />
          <YAxis 
            yAxisId="price"
            orientation="left"
            stroke="#2563eb"
            fontSize={12}
            domain={[minPrice - pricePadding, maxPrice + pricePadding]}
            tickFormatter={(value) => `$${value}`}
          />
          <YAxis 
            yAxisId="volume"
            orientation="right"
            stroke="#666"
            fontSize={12}
            domain={[0, maxVolume * 1.1]}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* Volume bars */}
          <Bar 
            yAxisId="volume"
            dataKey="buyVolume" 
            fill="#10b981" 
            fillOpacity={0.6}
            name="Buy Volume"
          />
          <Bar 
            yAxisId="volume"
            dataKey="sellVolume" 
            fill="#ef4444" 
            fillOpacity={0.6}
            name="Sell Volume"
          />
          
          {/* Price line */}
          <Line 
            yAxisId="price"
            type="monotone" 
            dataKey="price" 
            stroke="#2563eb" 
            strokeWidth={3}
            dot={<CustomDot />}
            activeDot={{ r: 6, fill: '#2563eb' }}
            name="Price"
          />
          
          {/* Reference line for equilibrium */}
          {chartData.some(d => d.imbalance === 0) && (
            <ReferenceLine 
              yAxisId="price"
              y={chartData.find(d => d.imbalance === 0)?.price} 
              stroke="#f59e0b" 
              strokeDasharray="5 5"
              label="Equilibrium"
            />
          )}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}
