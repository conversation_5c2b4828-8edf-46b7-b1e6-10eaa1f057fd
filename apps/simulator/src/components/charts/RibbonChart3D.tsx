import React, { useMemo, useRef, useEffect, useState } from 'react';
import Plot from 'react-plotly.js';
import type { ClockAuctionRound, ClockParticipant } from '../../types/types';

interface RibbonChart3DProps {
  rounds: ClockAuctionRound[];
  participants: ClockParticipant[];
  width?: number;
  height?: number;
  showBuyRibbons?: boolean;
  showSellRibbons?: boolean;
}

interface RibbonData {
  x: number[][]; // Price/Round data
  y: number[][]; // Quantity data
  z: number[][]; // Trader index data
  type: 'surface';
  colorscale: string[][];
  showscale: boolean;
  name: string;
  opacity: number;
}

export function RibbonChart3D({
  rounds,
  participants,
  width = 800,
  height = 600,
  showBuyRibbons = true,
  showSellRibbons = true
}: RibbonChart3DProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkVisibility = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const isCurrentlyVisible = rect.width > 0 && rect.height > 0 && rect.top < window.innerHeight && rect.bottom > 0;
        setIsVisible(isCurrentlyVisible);
      }
    };

    checkVisibility();
    const interval = setInterval(checkVisibility, 500);
    return () => clearInterval(interval);
  }, []);
  
  const ribbonData = useMemo(() => {
    if (rounds.length === 0 || participants.length === 0) {
      return [];
    }

    const data: RibbonData[] = [];
    
    // Create ribbon for each participant
    participants.forEach((participant, traderIndex) => {
      // Get all bids for this participant across rounds
      const participantBids = rounds.map(round => 
        round.bids.filter(bid => bid.participantId === participant.id)
      );
      
      // Create buy ribbon if enabled
      if (showBuyRibbons) {
        const buyX: number[] = [];
        const buyY: number[] = [];
        const buyZ: number[] = [];
        
        rounds.forEach((round, roundIndex) => {
          const buyBids = participantBids[roundIndex].filter(bid => bid.side === 'buy');
          
          if (buyBids.length > 0) {
            buyBids.forEach(bid => {
              buyX.push(round.announcedPrice);
              buyY.push(bid.quantity);
              buyZ.push(traderIndex);
            });
          } else {
            // Add zero point to maintain ribbon continuity
            buyX.push(round.announcedPrice);
            buyY.push(0);
            buyZ.push(traderIndex);
          }
        });
        
        if (buyX.length > 1) {
          // Create surface data for buy ribbon
          const surfaceX: number[][] = [];
          const surfaceY: number[][] = [];
          const surfaceZ: number[][] = [];
          
          // Create a surface by duplicating the line slightly offset
          for (let i = 0; i < buyX.length; i++) {
            surfaceX.push([buyX[i], buyX[i]]);
            surfaceY.push([buyY[i], buyY[i]]);
            surfaceZ.push([traderIndex - 0.1, traderIndex + 0.1]);
          }
          
          data.push({
            x: surfaceX,
            y: surfaceY,
            z: surfaceZ,
            type: 'surface',
            colorscale: [
              [0, '#dcfce7'], // Light green
              [0.5, '#22c55e'], // Medium green
              [1, '#15803d']  // Dark green
            ],
            showscale: false,
            name: `${participant.name} Buy`,
            opacity: 0.7
          });
        }
      }
      
      // Create sell ribbon if enabled
      if (showSellRibbons) {
        const sellX: number[] = [];
        const sellY: number[] = [];
        const sellZ: number[] = [];
        
        rounds.forEach((round, roundIndex) => {
          const sellBids = participantBids[roundIndex].filter(bid => bid.side === 'sell');
          
          if (sellBids.length > 0) {
            sellBids.forEach(bid => {
              sellX.push(round.announcedPrice);
              sellY.push(-bid.quantity); // Negative for sell side
              sellZ.push(traderIndex);
            });
          } else {
            // Add zero point to maintain ribbon continuity
            sellX.push(round.announcedPrice);
            sellY.push(0);
            sellZ.push(traderIndex);
          }
        });
        
        if (sellX.length > 1) {
          // Create surface data for sell ribbon
          const surfaceX: number[][] = [];
          const surfaceY: number[][] = [];
          const surfaceZ: number[][] = [];
          
          // Create a surface by duplicating the line slightly offset
          for (let i = 0; i < sellX.length; i++) {
            surfaceX.push([sellX[i], sellX[i]]);
            surfaceY.push([sellY[i], sellY[i]]);
            surfaceZ.push([traderIndex - 0.1, traderIndex + 0.1]);
          }
          
          data.push({
            x: surfaceX,
            y: surfaceY,
            z: surfaceZ,
            type: 'surface',
            colorscale: [
              [0, '#fee2e2'], // Light red
              [0.5, '#ef4444'], // Medium red
              [1, '#dc2626']  // Dark red
            ],
            showscale: false,
            name: `${participant.name} Sell`,
            opacity: 0.7
          });
        }
      }
    });

    return data;
  }, [rounds, participants, showBuyRibbons, showSellRibbons]);

  if (rounds.length === 0 || participants.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">🎗️</div>
          <div className="text-gray-500">3D Ribbon Chart will appear here</div>
          <div className="text-xs text-gray-400 mt-2">
            Shows trader demand/supply curves in 3D space
            <br />
            Y-axis: Quantity, X-axis: Price/Round, Z-axis: Traders
          </div>
        </div>
      </div>
    );
  }

  const layout = {
    title: {
      text: '3D Trader Demand/Supply Ribbons',
      font: { size: 16 }
    },
    scene: {
      xaxis: {
        title: 'Price ($)',
        showgrid: true,
        gridcolor: '#e5e7eb'
      },
      yaxis: {
        title: 'Quantity (Buy: +, Sell: -)',
        showgrid: true,
        gridcolor: '#e5e7eb',
        zeroline: true,
        zerolinecolor: '#6b7280',
        zerolinewidth: 2
      },
      zaxis: {
        title: 'Traders',
        showgrid: true,
        gridcolor: '#e5e7eb',
        tickmode: 'array',
        tickvals: participants.map((_, i) => i),
        ticktext: participants.map(p => p.name)
      },
      camera: {
        eye: { x: 1.5, y: 1.5, z: 1.5 },
        center: { x: 0, y: 0, z: 0 }
      },
      bgcolor: 'white'
    },
    width,
    height,
    margin: { l: 50, r: 50, t: 50, b: 50 },
    paper_bgcolor: 'white',
    plot_bgcolor: 'white'
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'pan2d',
      'select2d',
      'lasso2d',
      'resetScale2d',
      'autoScale2d'
    ],
    responsive: true
  };

  return (
    <div className="w-full" ref={containerRef}>
      {/* Controls */}
      <div className="mb-4 flex items-center space-x-4 text-sm">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="show-buy"
            checked={showBuyRibbons}
            onChange={() => {}} // Would need state management for interactive controls
            className="rounded"
          />
          <label htmlFor="show-buy" className="text-green-600 font-medium">
            Show Buy Ribbons (Green)
          </label>
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="show-sell"
            checked={showSellRibbons}
            onChange={() => {}} // Would need state management for interactive controls
            className="rounded"
          />
          <label htmlFor="show-sell" className="text-red-600 font-medium">
            Show Sell Ribbons (Red)
          </label>
        </div>
      </div>

      {/* 3D Plot */}
      <div
        style={{ width, height }}
        onMouseDown={(e) => e.stopPropagation()}
        onMouseMove={(e) => e.stopPropagation()}
        onMouseUp={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
        onTouchMove={(e) => e.stopPropagation()}
        onTouchEnd={(e) => e.stopPropagation()}
        className="plotly-chart-container"
      >
        {isVisible ? (
          <Plot
            data={ribbonData as any}
            layout={layout}
            config={config}
            style={{ width: '100%', height: '100%', pointerEvents: 'auto' }}
            useResizeHandler={true}
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-center">
              <div className="text-gray-400 text-lg mb-2">🎗️</div>
              <div className="text-gray-500">Loading 3D ribbons...</div>
            </div>
          </div>
        )}
      </div>

      {/* Legend and Info */}
      <div className="mt-4 text-xs text-gray-600">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <strong>Y-axis:</strong> Quantity (positive = buy, negative = sell)
          </div>
          <div>
            <strong>X-axis:</strong> Price per round
          </div>
          <div>
            <strong>Z-axis:</strong> Individual traders
          </div>
        </div>
        <div className="mt-2 text-center">
          Green ribbons show buy demand curves, red ribbons show sell supply curves
        </div>
      </div>
    </div>
  );
}
