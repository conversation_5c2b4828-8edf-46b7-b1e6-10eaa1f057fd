import React from 'react';
import { Group } from '@visx/group';
import { scaleLinear, scaleOrdinal } from '@visx/scale';
import { Bar } from '@visx/shape';
import type { ClockAuctionResult, ClockParticipant, Allocation } from '../../types/types';

interface AllocationFlowChartProps {
  auctionResult: ClockAuctionResult | null;
  participants: ClockParticipant[];
  width?: number;
  height?: number;
}

interface FlowNode {
  id: string;
  name: string;
  type: 'buyer' | 'seller' | 'market';
  x: number;
  y: number;
  width: number;
  height: number;
  value: number;
  color: string;
}

interface FlowLink {
  source: string;
  target: string;
  value: number;
  color: string;
  path: string;
}

const margin = { top: 40, right: 40, bottom: 40, left: 40 };

export function AllocationFlowChart({ 
  auctionResult, 
  participants,
  width = 800, 
  height = 400 
}: AllocationFlowChartProps) {
  
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  if (!auctionResult || auctionResult.allocations.length === 0) {
    return (
      <div className="flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300" style={{ width, height }}>
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">🔄</div>
          <div className="text-gray-500">Allocation flow will appear here</div>
          <div className="text-gray-400 text-sm mt-1">Complete an auction to see allocations</div>
        </div>
      </div>
    );
  }

  // Separate buyers and sellers
  const buyers = auctionResult.allocations.filter(a => a.side === 'buy');
  const sellers = auctionResult.allocations.filter(a => a.side === 'sell');
  
  const totalBuyVolume = buyers.reduce((sum, a) => sum + a.quantity, 0);
  const totalSellVolume = sellers.reduce((sum, a) => sum + a.quantity, 0);
  const matchedVolume = Math.min(totalBuyVolume, totalSellVolume);

  // Create nodes
  const nodes: FlowNode[] = [];
  const nodeHeight = 30;
  const nodeWidth = 120;
  const columnSpacing = innerWidth / 3;

  // Buyer nodes (left column)
  const buyerYScale = scaleLinear({
    domain: [0, buyers.length],
    range: [50, innerHeight - 50]
  });

  buyers.forEach((buyer, index) => {
    const participant = participants.find(p => p.id === buyer.participantId);
    nodes.push({
      id: buyer.participantId,
      name: participant?.name || buyer.participantId,
      type: 'buyer',
      x: 0,
      y: buyerYScale(index),
      width: nodeWidth,
      height: nodeHeight,
      value: buyer.quantity,
      color: '#10b981'
    });
  });

  // Market node (center)
  nodes.push({
    id: 'market',
    name: 'Market',
    type: 'market',
    x: columnSpacing,
    y: innerHeight / 2 - nodeHeight / 2,
    width: nodeWidth,
    height: nodeHeight * 2,
    value: matchedVolume,
    color: '#3b82f6'
  });

  // Seller nodes (right column)
  const sellerYScale = scaleLinear({
    domain: [0, sellers.length],
    range: [50, innerHeight - 50]
  });

  sellers.forEach((seller, index) => {
    const participant = participants.find(p => p.id === seller.participantId);
    nodes.push({
      id: seller.participantId + '_sell',
      name: participant?.name || seller.participantId,
      type: 'seller',
      x: columnSpacing * 2,
      y: sellerYScale(index),
      width: nodeWidth,
      height: nodeHeight,
      value: seller.quantity,
      color: '#ef4444'
    });
  });

  // Create links
  const links: FlowLink[] = [];

  // Links from buyers to market
  buyers.forEach(buyer => {
    const buyerNode = nodes.find(n => n.id === buyer.participantId);
    const marketNode = nodes.find(n => n.id === 'market');
    
    if (buyerNode && marketNode) {
      const sourceY = buyerNode.y + buyerNode.height / 2;
      const targetY = marketNode.y + marketNode.height / 4;
      
      links.push({
        source: buyer.participantId,
        target: 'market',
        value: buyer.quantity,
        color: '#10b981',
        path: createCurvedPath(
          buyerNode.x + buyerNode.width, sourceY,
          marketNode.x, targetY,
          buyer.quantity / matchedVolume * 20
        )
      });
    }
  });

  // Links from market to sellers
  sellers.forEach(seller => {
    const sellerNode = nodes.find(n => n.id === seller.participantId + '_sell');
    const marketNode = nodes.find(n => n.id === 'market');
    
    if (sellerNode && marketNode) {
      const sourceY = marketNode.y + marketNode.height * 3 / 4;
      const targetY = sellerNode.y + sellerNode.height / 2;
      
      links.push({
        source: 'market',
        target: seller.participantId + '_sell',
        value: seller.quantity,
        color: '#ef4444',
        path: createCurvedPath(
          marketNode.x + marketNode.width, sourceY,
          sellerNode.x, targetY,
          seller.quantity / matchedVolume * 20
        )
      });
    }
  });

  return (
    <div className="w-full">
      <div className="mb-4 text-center">
        <h3 className="text-lg font-semibold text-gray-900">Final Allocations</h3>
        <p className="text-sm text-gray-600">
          Matched Volume: {matchedVolume} units at ${auctionResult.endCondition.finalPrice}
        </p>
      </div>

      <svg width={width} height={height}>
        <Group left={margin.left} top={margin.top}>
          {/* Links */}
          {links.map((link, index) => (
            <path
              key={index}
              d={link.path}
              fill="none"
              stroke={link.color}
              strokeWidth={Math.max(2, link.value / matchedVolume * 20)}
              strokeOpacity={0.6}
            />
          ))}

          {/* Nodes */}
          {nodes.map(node => (
            <g key={node.id}>
              <rect
                x={node.x}
                y={node.y}
                width={node.width}
                height={node.height}
                fill={node.color}
                fillOpacity={0.8}
                stroke="#ffffff"
                strokeWidth={2}
                rx={4}
              />
              
              {/* Node label */}
              <text
                x={node.x + node.width / 2}
                y={node.y + node.height / 2 - 5}
                textAnchor="middle"
                fontSize={12}
                fill="white"
                fontWeight="medium"
              >
                {node.name.length > 12 ? node.name.substring(0, 12) + '...' : node.name}
              </text>
              
              {/* Node value */}
              <text
                x={node.x + node.width / 2}
                y={node.y + node.height / 2 + 8}
                textAnchor="middle"
                fontSize={10}
                fill="white"
                fontWeight="bold"
              >
                {node.type === 'market' ? `${node.value} units` : `${node.value}`}
              </text>
            </g>
          ))}

          {/* Column labels */}
          <text
            x={nodeWidth / 2}
            y={-10}
            textAnchor="middle"
            fontSize={14}
            fill="#374151"
            fontWeight="semibold"
          >
            Buyers
          </text>
          
          <text
            x={columnSpacing + nodeWidth / 2}
            y={-10}
            textAnchor="middle"
            fontSize={14}
            fill="#374151"
            fontWeight="semibold"
          >
            Market
          </text>
          
          <text
            x={columnSpacing * 2 + nodeWidth / 2}
            y={-10}
            textAnchor="middle"
            fontSize={14}
            fill="#374151"
            fontWeight="semibold"
          >
            Sellers
          </text>
        </Group>
      </svg>

      {/* Summary statistics */}
      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
        <div className="bg-green-50 p-3 rounded-lg">
          <div className="text-green-700 font-medium">Total Buy Orders</div>
          <div className="text-green-900 text-lg font-bold">{totalBuyVolume}</div>
          <div className="text-green-600 text-xs">{buyers.length} participants</div>
        </div>
        
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="text-blue-700 font-medium">Matched Volume</div>
          <div className="text-blue-900 text-lg font-bold">{matchedVolume}</div>
          <div className="text-blue-600 text-xs">
            {((matchedVolume / Math.max(totalBuyVolume, totalSellVolume)) * 100).toFixed(1)}% efficiency
          </div>
        </div>
        
        <div className="bg-red-50 p-3 rounded-lg">
          <div className="text-red-700 font-medium">Total Sell Orders</div>
          <div className="text-red-900 text-lg font-bold">{totalSellVolume}</div>
          <div className="text-red-600 text-xs">{sellers.length} participants</div>
        </div>
      </div>
    </div>
  );
}

function createCurvedPath(
  x1: number, y1: number,
  x2: number, y2: number,
  strokeWidth: number
): string {
  const midX = (x1 + x2) / 2;
  const controlX1 = x1 + (midX - x1) * 0.8;
  const controlX2 = x2 - (x2 - midX) * 0.8;
  
  return `M ${x1} ${y1} C ${controlX1} ${y1} ${controlX2} ${y2} ${x2} ${y2}`;
}
