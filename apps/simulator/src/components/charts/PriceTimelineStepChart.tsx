import React, { useMemo } from 'react';
import {
  <PERSON>sponsive<PERSON><PERSON>r,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine,
  LabelList
} from 'recharts';
import type { ClockAuctionRound } from '../../types/types';

interface PriceTimelineStepChartProps {
  rounds: ClockAuctionRound[];
  width?: number;
  height?: number;
  showRoundOverlay?: boolean;
}

interface StepChartDataPoint {
  price: number;
  buyVolume: number;
  sellVolume: number;
  roundNumber?: number;
  isReversal?: boolean;
}

export function PriceTimelineStepChart({ 
  rounds, 
  width = 800, 
  height = 400,
  showRoundOverlay = true
}: PriceTimelineStepChartProps) {
  
  const stepChartData = useMemo(() => {
    if (rounds.length === 0) return [];

    // Create price-quantity data points from rounds
    const pricePoints = new Map<number, StepChartDataPoint>();
    
    rounds.forEach(round => {
      const price = round.announcedPrice;
      const existing = pricePoints.get(price);
      
      if (existing) {
        // Update with latest round data for this price
        existing.buyVolume = round.totalBuyVolume;
        existing.sellVolume = round.totalSellVolume;
        existing.roundNumber = round.roundNumber;
      } else {
        pricePoints.set(price, {
          price,
          buyVolume: round.totalBuyVolume,
          sellVolume: round.totalSellVolume,
          roundNumber: round.roundNumber
        });
      }
    });

    // Convert to array and sort by price
    const sortedData = Array.from(pricePoints.values()).sort((a, b) => a.price - b.price);
    
    // Detect price reversals by checking if round numbers are not monotonic
    const roundSequence = rounds.map(r => r.roundNumber);
    let reversalDetected = false;
    for (let i = 1; i < roundSequence.length; i++) {
      if (roundSequence[i] < roundSequence[i - 1]) {
        reversalDetected = true;
        break;
      }
    }

    // Mark reversal points if detected
    if (reversalDetected && sortedData.length > 1) {
      // Find the price where reversal likely occurred
      const midIndex = Math.floor(sortedData.length / 2);
      sortedData[midIndex].isReversal = true;
    }

    return sortedData;
  }, [rounds]);

  // Custom tooltip showing round information
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = stepChartData.find(d => d.price === label);
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">Price: ${label}</p>
          {data?.roundNumber && (
            <p className="text-sm text-gray-600">Round: {data.roundNumber}</p>
          )}
          <div className="space-y-1 mt-2">
            <div className="flex justify-between text-sm">
              <span className="text-green-600">Buy Volume:</span>
              <span className="font-medium">{payload[0]?.value || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-red-600">Sell Volume:</span>
              <span className="font-medium">{payload[1]?.value || 0}</span>
            </div>
          </div>
          {data?.isReversal && (
            <p className="text-xs text-orange-600 mt-1">⚡ Price Reversal Point</p>
          )}
        </div>
      );
    }
    return null;
  };

  // Custom label for round numbers
  const RoundLabel = (props: any) => {
    const { x, y, payload } = props;
    if (!showRoundOverlay || !payload?.roundNumber) return null;
    
    return (
      <text 
        x={x} 
        y={y - 10} 
        textAnchor="middle" 
        fontSize={10} 
        fill="#666"
        fontWeight="bold"
      >
        R{payload.roundNumber}
      </text>
    );
  };

  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📈</div>
          <div className="text-gray-500">Price timeline step chart will appear here</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={stepChartData}
          margin={{ top: 30, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="price"
            stroke="#666"
            fontSize={12}
            label={{
              value: "Price ($)",
              position: "insideBottom",
              offset: -10,
              style: { textAnchor: 'middle' }
            }}
          />
          <YAxis
            stroke="#666"
            fontSize={12}
            label={{
              value: "Volume",
              angle: -90,
              position: "insideLeft",
              style: { textAnchor: 'middle' }
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend verticalAlign="top" height={36} />
          
          {/* Buy Volume Step Line */}
          <Line
            type="step"
            dataKey="buyVolume"
            name="Buy Volume"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ r: 4, fill: '#10b981' }}
            activeDot={{ r: 6, fill: '#10b981' }}
          >
            {showRoundOverlay && <LabelList content={RoundLabel} />}
          </Line>
          
          {/* Sell Volume Step Line */}
          <Line
            type="step"
            dataKey="sellVolume"
            name="Sell Volume"
            stroke="#ef4444"
            strokeWidth={2}
            dot={{ r: 4, fill: '#ef4444' }}
            activeDot={{ r: 6, fill: '#ef4444' }}
          />

          {/* Mark reversal points */}
          {stepChartData.filter(d => d.isReversal).map(point => (
            <ReferenceLine
              key={`reversal-${point.price}`}
              x={point.price}
              stroke="#f59e0b"
              strokeWidth={2}
              strokeDasharray="5 5"
              label={{ value: "Reversal", position: "top" }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      
      {/* Info text */}
      <div className="text-xs text-gray-500 mt-2 text-center">
        Quantity × Price step chart showing volume at each price level
        {showRoundOverlay && " with round number overlay"}
      </div>
    </div>
  );
}
