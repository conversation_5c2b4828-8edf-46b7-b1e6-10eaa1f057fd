import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { ClockAuctionResult, ClockParticipant, ClockAuctionRound } from '../../types/types';

interface SankeyAllocationFlowProps {
  auctionResult?: ClockAuctionResult | null;
  participants: ClockParticipant[];
  rounds: ClockAuctionRound[];
  width?: number;
  height?: number;
}

interface SankeyData {
  labels: string[];
  source: number[];
  target: number[];
  value: number[];
}

export function SankeyAllocationFlow({ 
  auctionResult, 
  participants, 
  rounds,
  width = 800, 
  height = 400 
}: SankeyAllocationFlowProps) {
  
  const sankeyData = useMemo((): SankeyData => {
    if (rounds.length === 0 || participants.length === 0) {
      return { labels: [], source: [], target: [], value: [] };
    }

    // Use the latest round for flow visualization
    const latestRound = rounds[rounds.length - 1];
    
    // Separate participants into buyers and sellers based on their bids
    const sellers = participants.filter(p => {
      const participantBids = latestRound.bids.filter(bid => bid.participantId === p.id);
      return participantBids.some(bid => bid.side === 'sell' && bid.quantity > 0);
    });
    
    const buyers = participants.filter(p => {
      const participantBids = latestRound.bids.filter(bid => bid.participantId === p.id);
      return participantBids.some(bid => bid.side === 'buy' && bid.quantity > 0);
    });

    if (sellers.length === 0 && buyers.length === 0) {
      return { labels: [], source: [], target: [], value: [] };
    }

    const labels: string[] = [];
    const source: number[] = [];
    const target: number[] = [];
    const value: number[] = [];

    // Column 1: Seller Max Constraints
    sellers.forEach(seller => {
      labels.push(`${seller.name}\n(Max: ${seller.curveParams.maxSellQuantity})`);
    });

    // Column 2: Seller Orders
    sellers.forEach(seller => {
      const sellBids = latestRound.bids.filter(bid => 
        bid.participantId === seller.id && bid.side === 'sell'
      );
      const totalSellQuantity = sellBids.reduce((sum, bid) => sum + bid.quantity, 0);
      labels.push(`${seller.name}\nSell: ${totalSellQuantity}`);
    });

    // Column 3: Buyer Orders
    buyers.forEach(buyer => {
      const buyBids = latestRound.bids.filter(bid => 
        bid.participantId === buyer.id && bid.side === 'buy'
      );
      const totalBuyQuantity = buyBids.reduce((sum, bid) => sum + bid.quantity, 0);
      labels.push(`${buyer.name}\nBuy: ${totalBuyQuantity}`);
    });

    // Column 4: Buyer Max Constraints
    buyers.forEach(buyer => {
      labels.push(`${buyer.name}\n(Max: ${Math.abs(buyer.curveParams.maxBuyQuantity)})`);
    });

    // Create flows
    // Column 1 → 2: Max constraints to seller orders
    sellers.forEach((seller, index) => {
      const sellBids = latestRound.bids.filter(bid => 
        bid.participantId === seller.id && bid.side === 'sell'
      );
      const totalSellQuantity = sellBids.reduce((sum, bid) => sum + bid.quantity, 0);
      
      if (totalSellQuantity > 0) {
        source.push(index); // max constraint node
        target.push(sellers.length + index); // sell order node
        value.push(totalSellQuantity);
      }
    });

    // Column 2 → 3: Seller orders to buyer orders (simplified matching)
    // In a real auction, this would use actual allocation data
    // For simulation, we'll create proportional flows
    const totalSellVolume = latestRound.totalSellVolume;
    const totalBuyVolume = latestRound.totalBuyVolume;
    const matchedVolume = Math.min(totalSellVolume, totalBuyVolume);

    if (matchedVolume > 0) {
      sellers.forEach((seller, sellerIndex) => {
        const sellBids = latestRound.bids.filter(bid => 
          bid.participantId === seller.id && bid.side === 'sell'
        );
        const sellerQuantity = sellBids.reduce((sum, bid) => sum + bid.quantity, 0);
        
        if (sellerQuantity > 0) {
          buyers.forEach((buyer, buyerIndex) => {
            const buyBids = latestRound.bids.filter(bid => 
              bid.participantId === buyer.id && bid.side === 'buy'
            );
            const buyerQuantity = buyBids.reduce((sum, bid) => sum + bid.quantity, 0);
            
            if (buyerQuantity > 0) {
              // Proportional allocation
              const flow = Math.min(
                sellerQuantity * (buyerQuantity / totalBuyVolume),
                buyerQuantity * (sellerQuantity / totalSellVolume)
              );
              
              if (flow > 0.1) { // Only show significant flows
                source.push(sellers.length + sellerIndex); // sell order node
                target.push(sellers.length * 2 + buyerIndex); // buy order node
                value.push(Math.round(flow * 10) / 10); // Round to 1 decimal
              }
            }
          });
        }
      });
    }

    // Column 3 → 4: Buyer orders to max constraints
    buyers.forEach((buyer, index) => {
      const buyBids = latestRound.bids.filter(bid => 
        bid.participantId === buyer.id && bid.side === 'buy'
      );
      const totalBuyQuantity = buyBids.reduce((sum, bid) => sum + bid.quantity, 0);
      
      if (totalBuyQuantity > 0) {
        source.push(sellers.length * 2 + index); // buy order node
        target.push(sellers.length * 2 + buyers.length + index); // max constraint node
        value.push(totalBuyQuantity);
      }
    });

    return { labels, source, target, value };
  }, [auctionResult, participants, rounds]);

  if (sankeyData.labels.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">🔄</div>
          <div className="text-gray-500">Allocation flow will appear here</div>
          <div className="text-xs text-gray-400 mt-2">
            Shows flow from seller constraints → orders → buyer orders → constraints
          </div>
        </div>
      </div>
    );
  }

  const data = [
    {
      type: 'sankey',
      orientation: 'h',
      node: {
        label: sankeyData.labels,
        color: sankeyData.labels.map((_, index) => {
          const totalNodes = sankeyData.labels.length;
          const sellersCount = participants.filter(p => 
            rounds[rounds.length - 1]?.bids.some(bid => 
              bid.participantId === p.id && bid.side === 'sell'
            )
          ).length;
          const buyersCount = participants.filter(p => 
            rounds[rounds.length - 1]?.bids.some(bid => 
              bid.participantId === p.id && bid.side === 'buy'
            )
          ).length;
          
          // Color coding: sellers (red tones), buyers (green tones)
          if (index < sellersCount * 2) {
            return index < sellersCount ? '#fca5a5' : '#ef4444'; // Light red to red
          } else {
            return index < sellersCount * 2 + buyersCount ? '#86efac' : '#22c55e'; // Light green to green
          }
        }),
        hovertemplate: '<b>%{label}</b><br>Total Flow: %{value}<extra></extra>',
      },
      link: {
        source: sankeyData.source,
        target: sankeyData.target,
        value: sankeyData.value,
        color: sankeyData.source.map(() => 'rgba(100, 100, 100, 0.3)'),
        hovertemplate: '<b>%{source.label} → %{target.label}</b><br>Flow: %{value} units<extra></extra>',
      },
    },
  ];

  const layout = {
    title: {
      text: `Auction Flow - Round ${rounds[rounds.length - 1]?.roundNumber || 0}`,
      font: { size: 16 },
    },
    width,
    height,
    margin: { l: 50, r: 50, t: 50, b: 50 },
    font: { size: 11 },
    paper_bgcolor: 'white',
    plot_bgcolor: 'white',
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'pan2d',
      'select2d',
      'lasso2d',
      'resetScale2d',
      'autoScale2d',
    ],
    responsive: true,
  };

  return (
    <div style={{ width, height }}>
      <Plot
        data={data}
        layout={layout}
        config={config}
        style={{ width: '100%', height: '100%' }}
        useResizeHandler={true}
      />
    </div>
  );
}
