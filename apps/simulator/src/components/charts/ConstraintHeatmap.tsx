import React from 'react';
import type { ClockAuctionRound, ClockParticipant, BidderConstraints } from '../../types/types';

interface ConstraintHeatmapProps {
  rounds: ClockAuctionRound[];
  participants: ClockParticipant[];
  width?: number;
  height?: number;
}

interface ConstraintCell {
  round: number;
  participantId: string;
  participantName: string;
  maxBuy: number;
  minBuy: number;
  maxSell: number;
  minSell: number;
  utilization: number; // 0-1 scale of how constrained they are
  violationRisk: number; // 0-1 scale of risk of constraint violation
}

export function ConstraintHeatmap({ 
  rounds, 
  participants,
  width = 800, 
  height = 400 
}: ConstraintHeatmapProps) {
  
  if (rounds.length === 0 || participants.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">🔥</div>
          <div className="text-gray-500">Constraint heatmap will appear here</div>
        </div>
      </div>
    );
  }

  // Calculate constraint data for each round and participant
  const constraintData: ConstraintCell[] = [];
  
  rounds.forEach(round => {
    participants.forEach(participant => {
      const participantConstraints = round.constraints.find(c => c.participantId === participant.id);
      const participantBids = round.bids.filter(bid => bid.participantId === participant.id);
      
      if (participantConstraints) {
        const buyBid = participantBids.find(bid => bid.side === 'buy');
        const sellBid = participantBids.find(bid => bid.side === 'sell');
        
        // Calculate utilization (how close to constraints)
        let utilization = 0;
        if (buyBid) {
          const buyRange = participantConstraints.maxBuy - participantConstraints.minBuy;
          if (buyRange > 0) {
            utilization = Math.max(utilization, 
              (buyBid.quantity - participantConstraints.minBuy) / buyRange
            );
          }
        }
        if (sellBid) {
          const sellRange = participantConstraints.maxSell - participantConstraints.minSell;
          if (sellRange > 0) {
            utilization = Math.max(utilization, 
              (sellBid.quantity - participantConstraints.minSell) / sellRange
            );
          }
        }

        // Calculate violation risk (how close to constraint boundaries)
        let violationRisk = 0;
        if (buyBid) {
          const buyMargin = Math.min(
            buyBid.quantity - participantConstraints.minBuy,
            participantConstraints.maxBuy - buyBid.quantity
          );
          violationRisk = Math.max(violationRisk, 1 - (buyMargin / Math.max(1, participantConstraints.maxBuy)));
        }
        if (sellBid) {
          const sellMargin = Math.min(
            sellBid.quantity - participantConstraints.minSell,
            participantConstraints.maxSell - sellBid.quantity
          );
          violationRisk = Math.max(violationRisk, 1 - (sellMargin / Math.max(1, participantConstraints.maxSell)));
        }

        constraintData.push({
          round: round.roundNumber,
          participantId: participant.id,
          participantName: participant.name,
          maxBuy: participantConstraints.maxBuy,
          minBuy: participantConstraints.minBuy,
          maxSell: participantConstraints.maxSell,
          minSell: participantConstraints.minSell,
          utilization: Math.min(1, utilization),
          violationRisk: Math.min(1, violationRisk)
        });
      }
    });
  });

  const cellWidth = Math.max(40, (width - 200) / rounds.length);
  const cellHeight = Math.max(30, (height - 100) / participants.length);
  const actualWidth = 200 + cellWidth * rounds.length;
  const actualHeight = 100 + cellHeight * participants.length;

  // Get color for constraint level
  const getConstraintColor = (utilization: number, violationRisk: number) => {
    if (violationRisk > 0.8) return '#dc2626'; // High risk - red
    if (violationRisk > 0.6) return '#f59e0b'; // Medium risk - yellow
    if (utilization > 0.7) return '#3b82f6'; // High utilization - blue
    if (utilization > 0.3) return '#10b981'; // Medium utilization - green
    return '#e5e7eb'; // Low activity - gray
  };

  const getConstraintIntensity = (utilization: number, violationRisk: number) => {
    return Math.max(utilization, violationRisk);
  };

  return (
    <div className="w-full overflow-x-auto">
      <div className="relative" style={{ width: actualWidth, height: actualHeight }}>
        <svg width={actualWidth} height={actualHeight}>
          {/* Background */}
          <rect width={actualWidth} height={actualHeight} fill="#fafafa" />
          
          {/* Column headers (rounds) */}
          {rounds.map((round, roundIndex) => (
            <g key={round.roundNumber}>
              <text
                x={200 + roundIndex * cellWidth + cellWidth / 2}
                y={30}
                textAnchor="middle"
                fontSize={12}
                fill="#374151"
                fontWeight="medium"
              >
                R{round.roundNumber}
              </text>
              <text
                x={200 + roundIndex * cellWidth + cellWidth / 2}
                y={45}
                textAnchor="middle"
                fontSize={10}
                fill="#6b7280"
              >
                ${round.announcedPrice}
              </text>
            </g>
          ))}

          {/* Row headers (participants) */}
          {participants.map((participant, participantIndex) => (
            <text
              key={participant.id}
              x={190}
              y={70 + participantIndex * cellHeight + cellHeight / 2 + 4}
              textAnchor="end"
              fontSize={11}
              fill="#374151"
              fontWeight="medium"
            >
              {participant.name.length > 15 ? 
                participant.name.substring(0, 15) + '...' : 
                participant.name
              }
            </text>
          ))}

          {/* Heatmap cells */}
          {constraintData.map(cell => {
            const roundIndex = rounds.findIndex(r => r.roundNumber === cell.round);
            const participantIndex = participants.findIndex(p => p.participantId === cell.participantId);
            
            if (roundIndex === -1 || participantIndex === -1) return null;

            const x = 200 + roundIndex * cellWidth;
            const y = 60 + participantIndex * cellHeight;
            const color = getConstraintColor(cell.utilization, cell.violationRisk);
            const intensity = getConstraintIntensity(cell.utilization, cell.violationRisk);

            return (
              <g key={`${cell.round}-${cell.participantId}`}>
                <rect
                  x={x}
                  y={y}
                  width={cellWidth - 1}
                  height={cellHeight - 1}
                  fill={color}
                  fillOpacity={0.3 + intensity * 0.7}
                  stroke="#ffffff"
                  strokeWidth={1}
                />
                
                {/* Constraint values text */}
                {cellWidth > 50 && cellHeight > 35 && (
                  <g>
                    <text
                      x={x + cellWidth / 2}
                      y={y + cellHeight / 2 - 5}
                      textAnchor="middle"
                      fontSize={9}
                      fill="#1f2937"
                      fontWeight="medium"
                    >
                      {cell.maxBuy > 0 ? `B:${cell.maxBuy}` : ''}
                    </text>
                    <text
                      x={x + cellWidth / 2}
                      y={y + cellHeight / 2 + 8}
                      textAnchor="middle"
                      fontSize={9}
                      fill="#1f2937"
                      fontWeight="medium"
                    >
                      {cell.maxSell > 0 ? `S:${cell.maxSell}` : ''}
                    </text>
                  </g>
                )}

                {/* Tooltip trigger (invisible rect) */}
                <rect
                  x={x}
                  y={y}
                  width={cellWidth - 1}
                  height={cellHeight - 1}
                  fill="transparent"
                  className="cursor-pointer"
                >
                  <title>
                    {`${cell.participantName} - Round ${cell.round}
Max Buy: ${cell.maxBuy}, Min Buy: ${cell.minBuy}
Max Sell: ${cell.maxSell}, Min Sell: ${cell.minSell}
Utilization: ${(cell.utilization * 100).toFixed(1)}%
Risk: ${(cell.violationRisk * 100).toFixed(1)}%`}
                  </title>
                </rect>
              </g>
            );
          })}

          {/* Grid lines */}
          {/* Vertical lines */}
          {rounds.map((_, roundIndex) => (
            <line
              key={`v-${roundIndex}`}
              x1={200 + roundIndex * cellWidth}
              y1={60}
              x2={200 + roundIndex * cellWidth}
              y2={60 + participants.length * cellHeight}
              stroke="#d1d5db"
              strokeWidth={0.5}
            />
          ))}
          
          {/* Horizontal lines */}
          {participants.map((_, participantIndex) => (
            <line
              key={`h-${participantIndex}`}
              x1={200}
              y1={60 + participantIndex * cellHeight}
              x2={200 + rounds.length * cellWidth}
              y2={60 + participantIndex * cellHeight}
              stroke="#d1d5db"
              strokeWidth={0.5}
            />
          ))}
        </svg>

        {/* Legend */}
        <div className="absolute top-4 right-4 bg-white bg-opacity-95 p-3 rounded-lg border border-gray-200 text-xs">
          <div className="font-medium text-gray-900 mb-2">Constraint Status</div>
          <div className="space-y-1">
            <div className="flex items-center">
              <div className="w-4 h-3 bg-red-600 bg-opacity-70 mr-2 rounded"></div>
              <span>High Risk</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-3 bg-yellow-500 bg-opacity-70 mr-2 rounded"></div>
              <span>Medium Risk</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-3 bg-blue-500 bg-opacity-70 mr-2 rounded"></div>
              <span>High Utilization</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-3 bg-green-500 bg-opacity-70 mr-2 rounded"></div>
              <span>Active</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-3 bg-gray-300 mr-2 rounded"></div>
              <span>Inactive</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
