import React from 'react';
import { BaseAgGrid } from './BaseAgGrid';
import type { ColDef } from 'ag-grid-community';
import type { ClockParticipant, ClockAuctionRound } from '../types/types';

interface ParticipantsTableProps {
  participants: ClockParticipant[];
  rounds: ClockAuctionRound[];
  currentPrice?: number;
}

interface ParticipantRowData {
  id: string;
  name: string;
  type: string;
  maxBuy: number;
  maxSell: number;
  zeroPrice: number;
  slope: number;
  currentBid: number;
  currentSide: 'buy' | 'sell' | 'none';
  totalVolume: number;
  status: string;
}

export function ParticipantsTable({ 
  participants, 
  rounds, 
  currentPrice = 0 
}: ParticipantsTableProps) {
  
  // Get the latest round for current bid information
  const latestRound = rounds.length > 0 ? rounds[rounds.length - 1] : null;

  // Transform participants data for the grid
  const rowData: ParticipantRowData[] = participants.map(participant => {
    // Calculate current bid at current price
    const currentQuantity = participant.quantityFunction(currentPrice);
    const currentSide = currentQuantity > 0 ? 'sell' : currentQuantity < 0 ? 'buy' : 'none';
    const currentBid = Math.abs(currentQuantity);

    // Calculate total volume across all rounds
    const totalVolume = rounds.reduce((sum, round) => {
      const participantBids = round.bids.filter(bid => bid.participantId === participant.id);
      return sum + participantBids.reduce((bidSum, bid) => bidSum + bid.quantity, 0);
    }, 0);

    // Determine participant type based on curve parameters
    let type = 'Unknown';
    if (participant.curveParams.maxBuyQuantity < 0 && participant.curveParams.maxSellQuantity === 0) {
      type = 'Buyer';
    } else if (participant.curveParams.maxBuyQuantity === 0 && participant.curveParams.maxSellQuantity > 0) {
      type = 'Seller';
    } else if (participant.curveParams.maxBuyQuantity < 0 && participant.curveParams.maxSellQuantity > 0) {
      type = 'Market Maker';
    }

    // Determine status
    let status = 'Active';
    if (currentBid === 0) {
      status = 'Inactive';
    } else if (latestRound) {
      const hasCurrentBid = latestRound.bids.some(bid => bid.participantId === participant.id);
      status = hasCurrentBid ? 'Bidding' : 'Inactive';
    }

    return {
      id: participant.id,
      name: participant.name,
      type,
      maxBuy: Math.abs(participant.curveParams.maxBuyQuantity),
      maxSell: participant.curveParams.maxSellQuantity,
      zeroPrice: participant.curveParams.zeroPrice,
      slope: participant.curveParams.slope,
      currentBid,
      currentSide,
      totalVolume,
      status
    };
  });

  // Column definitions
  const columnDefs: ColDef[] = [
    {
      headerName: 'Participant',
      field: 'name',
      width: 180,
      pinned: 'left',
      cellStyle: { fontWeight: 'bold' }
    },
    {
      headerName: 'Type',
      field: 'type',
      width: 120,
      cellStyle: (params) => {
        switch (params.value) {
          case 'Buyer': return { color: '#10b981', fontWeight: 'medium' };
          case 'Seller': return { color: '#ef4444', fontWeight: 'medium' };
          case 'Market Maker': return { color: '#3b82f6', fontWeight: 'medium' };
          default: return {};
        }
      }
    },
    {
      headerName: 'Max Buy',
      field: 'maxBuy',
      width: 100,
      type: 'numericColumn',
      valueFormatter: (params) => params.value.toFixed(0)
    },
    {
      headerName: 'Max Sell',
      field: 'maxSell',
      width: 100,
      type: 'numericColumn',
      valueFormatter: (params) => params.value.toFixed(0)
    },
    {
      headerName: 'Zero Price',
      field: 'zeroPrice',
      width: 110,
      type: 'numericColumn',
      valueFormatter: (params) => `$${params.value.toFixed(2)}`
    },
    {
      headerName: 'Slope',
      field: 'slope',
      width: 90,
      type: 'numericColumn',
      valueFormatter: (params) => params.value.toFixed(2)
    },
    {
      headerName: 'Current Bid',
      field: 'currentBid',
      width: 120,
      type: 'numericColumn',
      valueFormatter: (params) => params.value.toFixed(1),
      cellStyle: (params) => {
        if (params.data.currentSide === 'buy') {
          return { color: '#10b981', fontWeight: 'bold' };
        } else if (params.data.currentSide === 'sell') {
          return { color: '#ef4444', fontWeight: 'bold' };
        }
        return { color: '#6b7280' };
      }
    },
    {
      headerName: 'Side',
      field: 'currentSide',
      width: 80,
      cellStyle: (params) => {
        switch (params.value) {
          case 'buy': return { color: '#10b981', fontWeight: 'bold', textTransform: 'uppercase' };
          case 'sell': return { color: '#ef4444', fontWeight: 'bold', textTransform: 'uppercase' };
          default: return { color: '#6b7280', textTransform: 'uppercase' };
        }
      }
    },
    {
      headerName: 'Total Volume',
      field: 'totalVolume',
      width: 130,
      type: 'numericColumn',
      valueFormatter: (params) => params.value.toFixed(1)
    },
    {
      headerName: 'Status',
      field: 'status',
      width: 100,
      cellStyle: (params) => {
        switch (params.value) {
          case 'Bidding': return { color: '#10b981', fontWeight: 'medium' };
          case 'Active': return { color: '#3b82f6', fontWeight: 'medium' };
          case 'Inactive': return { color: '#6b7280', fontWeight: 'medium' };
          default: return {};
        }
      }
    }
  ];

  return (
    <div className="w-full">
      <div className="mb-3 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Participants ({participants.length})
        </h3>
        {currentPrice > 0 && (
          <div className="text-sm text-gray-600">
            Current Price: <span className="font-medium text-blue-600">${currentPrice.toFixed(2)}</span>
          </div>
        )}
      </div>
      
      <BaseAgGrid
        rowData={rowData}
        columnDefs={columnDefs}
        height={300}
        width="100%"
        enableSorting={true}
        enableFiltering={false}
        enablePagination={false}
        className=""
        gridOptions={{
          suppressHorizontalScroll: false,
          suppressColumnVirtualisation: true,
          animateRows: true,
          rowHeight: 40,
        }}
      />
    </div>
  );
}
