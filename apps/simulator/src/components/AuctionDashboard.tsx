import React from 'react';
import { PriceTimelineChart } from './charts/PriceTimelineChart';
import { SupplyDemandChart } from './charts/SupplyDemandChart';
import { VolumeAnalysisChart } from './charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from './charts/ConstraintHeatmap';
import { AllocationFlowChart } from './charts/AllocationFlowChart';
import { ParticipantsTable } from './ParticipantsTable';
import type { ClockAuctionResult, ClockAuctionRound, ClockParticipant } from '../types/types';

interface AuctionDashboardProps {
  rounds: ClockAuctionRound[];
  auctionResult: ClockAuctionResult | null;
  currentRound: number;
  participants: ClockParticipant[];
}

export function AuctionDashboard({
  rounds,
  auctionResult,
  participants
}: AuctionDashboardProps) {

  if (rounds.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <div className="text-gray-500 text-lg">
          🎯 Ready to start auction simulation
        </div>
        <p className="text-gray-400 mt-2">
          Click "Start New Auction" to begin the double-sided clock auction
        </p>
      </div>
    );
  }

  const latestRound = rounds[rounds.length - 1];
  const currentPrice = latestRound.announcedPrice;

  return (
    <div className="dashboard-grid">
      {/* Status Cards */}
      <div className="status-card">
        <h3 className="text-sm font-medium text-gray-500 mb-1">Current Price</h3>
        <div className="text-2xl font-bold text-blue-600">
          ${latestRound.announcedPrice}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Round {latestRound.roundNumber}
        </div>
      </div>

      <div className="status-card">
        <h3 className="text-sm font-medium text-gray-500 mb-1">Buy Volume</h3>
        <div className="text-2xl font-bold text-green-600">
          {latestRound.totalBuyVolume}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Total demand
        </div>
      </div>

      <div className="status-card">
        <h3 className="text-sm font-medium text-gray-500 mb-1">Sell Volume</h3>
        <div className="text-2xl font-bold text-red-600">
          {latestRound.totalSellVolume}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Total supply
        </div>
      </div>

      <div className="status-card">
        <h3 className="text-sm font-medium text-gray-500 mb-1">Imbalance</h3>
        <div className={`text-2xl font-bold ${
          latestRound.totalBuyVolume - latestRound.totalSellVolume > 0
            ? 'text-green-600'
            : latestRound.totalBuyVolume - latestRound.totalSellVolume < 0
              ? 'text-red-600'
              : 'text-gray-600'
        }`}>
          {latestRound.totalBuyVolume - latestRound.totalSellVolume > 0 ? '+' : ''}
          {latestRound.totalBuyVolume - latestRound.totalSellVolume}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Buy - Sell
        </div>
      </div>

      {/* Price Timeline Chart */}
      <div className="chart-card">
        <h3 className="chart-title">📈 Price Discovery Timeline</h3>
        <PriceTimelineChart rounds={rounds} height={300} showVolumes={true} />
      </div>

      {/* Supply & Demand Chart */}
      <div className="chart-card">
        <h3 className="chart-title">📊 Supply & Demand Curves</h3>
        <SupplyDemandChart
          participants={participants}
          rounds={rounds}
          currentPrice={currentPrice}
          height={300}
        />
      </div>

      {/* Volume Analysis Chart */}
      <div className="chart-card">
        <h3 className="chart-title">📊 Volume Analysis</h3>
        <VolumeAnalysisChart
          rounds={rounds}
          participants={participants}
          height={300}
          chartType="stacked"
        />
      </div>

      {/* Constraint Heatmap */}
      <div className="chart-card">
        <h3 className="chart-title">🔥 Constraint Tracking</h3>
        <ConstraintHeatmap
          rounds={rounds}
          participants={participants}
          height={300}
        />
      </div>

      {/* Participants Table */}
      <div className="table-card">
        <ParticipantsTable
          participants={participants}
          rounds={rounds}
          currentPrice={currentPrice}
        />
      </div>

      {/* Allocation Flow (only when auction is complete) */}
      {auctionResult && (
        <div className="allocation-card">
          <h3 className="chart-title">🔄 Final Allocations</h3>
          <AllocationFlowChart
            auctionResult={auctionResult}
            participants={participants}
            height={400}
          />
        </div>
      )}

      {/* Auction Result */}
      {auctionResult && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-4">🏁 Auction Complete!</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm text-green-700">End Condition</div>
              <div className="font-medium text-green-900">{auctionResult.endCondition.type}</div>
            </div>
            <div>
              <div className="text-sm text-green-700">Final Price</div>
              <div className="font-medium text-green-900">${auctionResult.endCondition.finalPrice}</div>
            </div>
            <div>
              <div className="text-sm text-green-700">Matched Quantity</div>
              <div className="font-medium text-green-900">{auctionResult.endCondition.matchedQuantity}</div>
            </div>
          </div>
          
          {auctionResult.allocations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-green-900 mb-2">Allocations</h4>
              <div className="space-y-2">
                {auctionResult.allocations.map((allocation, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{allocation.participantId}</span>
                    <span className={allocation.side === 'buy' ? 'text-green-600' : 'text-red-600'}>
                      {allocation.side} {allocation.quantity} @ ${allocation.price}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
