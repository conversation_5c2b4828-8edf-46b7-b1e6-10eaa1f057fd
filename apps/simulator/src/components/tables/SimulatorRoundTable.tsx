import React, { useMemo } from 'react';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import { BaseAgGrid } from '../BaseAgGrid';
import type { ClockAuctionRound, ClockParticipant, ClockBid } from '../../types/types';

interface SimulatorRoundTableProps {
  rounds: ClockAuctionRound[];
  participants: ClockParticipant[];
  height: number;
  selectedRound?: number;
  onSelectedRoundChange?: (round: number) => void;
  onParticipantClick?: (participantId: string) => void;
}

interface TableRow {
  id: string;
  type: 'participant' | 'price' | 'totals';
  participantId?: string;
  participantName?: string;
  bids: { [roundNumber: number]: ClockBid | null };
}

// Cell renderer for bid data
const BidCell: React.FC<ICellRendererParams> = (params) => {
  const { data, colDef } = params;
  const roundNumber = parseInt(colDef?.headerName || '0');
  
  if (data.type === 'price') {
    const round = params.context?.rounds?.find((r: ClockAuctionRound) => r.roundNumber === roundNumber);
    if (round) {
      return (
        <div className="flex items-center justify-center h-full bg-blue-50 font-semibold text-blue-800">
          ${round.announcedPrice}
        </div>
      );
    }
    return <div />;
  }
  
  if (data.type === 'totals') {
    const round = params.context?.rounds?.find((r: ClockAuctionRound) => r.roundNumber === roundNumber);
    if (round) {
      return (
        <div className="flex flex-col items-center justify-center h-full bg-gray-50 text-xs">
          <div className="text-green-600 font-medium">B: {round.totalBuyVolume}</div>
          <div className="text-red-600 font-medium">S: {round.totalSellVolume}</div>
        </div>
      );
    }
    return <div />;
  }
  
  if (data.type === 'participant') {
    const bid = data.bids[roundNumber];
    if (bid) {
      const isBuy = bid.side === 'buy';
      const bgColor = isBuy ? 'bg-green-100' : 'bg-red-100';
      const textColor = isBuy ? 'text-green-800' : 'text-red-800';
      const sideLabel = isBuy ? 'B' : 'S';
      
      return (
        <div className={`flex items-center justify-center h-full ${bgColor} ${textColor} font-medium text-sm`}>
          {sideLabel}: {bid.quantity}
        </div>
      );
    }
    return <div className="flex items-center justify-center h-full bg-gray-50 text-gray-400 text-xs">-</div>;
  }
  
  return <div />;
};

// Header cell renderer for round selection
const RoundHeaderCell: React.FC<ICellRendererParams> = (params) => {
  const roundNumber = parseInt(params.displayName || '0');
  const isSelected = params.context?.selectedRound === roundNumber;
  const onRoundClick = params.context?.onRoundClick;
  
  return (
    <div 
      className={`flex items-center justify-center h-full cursor-pointer font-semibold ${
        isSelected ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      onClick={() => onRoundClick?.(roundNumber)}
    >
      R{roundNumber}
    </div>
  );
};

export function SimulatorRoundTable({
  rounds,
  participants,
  height,
  selectedRound = 1,
  onSelectedRoundChange,
  onParticipantClick
}: SimulatorRoundTableProps) {
  
  // Transform data for AG Grid
  const { rowData, columnDefs } = useMemo(() => {
    if (rounds.length === 0 || participants.length === 0) {
      return { rowData: [], columnDefs: [] };
    }

    // Create row data
    const rows: TableRow[] = [];
    
    // Price row
    rows.push({
      id: 'price',
      type: 'price',
      bids: {}
    });
    
    // Participant rows
    participants.forEach(participant => {
      const participantBids: { [roundNumber: number]: ClockBid | null } = {};
      
      rounds.forEach(round => {
        const bid = round.bids.find(b => b.participantId === participant.id);
        participantBids[round.roundNumber] = bid || null;
      });
      
      rows.push({
        id: participant.id,
        type: 'participant',
        participantId: participant.id,
        participantName: participant.name,
        bids: participantBids
      });
    });
    
    // Totals row
    rows.push({
      id: 'totals',
      type: 'totals',
      bids: {}
    });

    // Create column definitions
    const cols: ColDef[] = [
      {
        colId: 'participant',
        headerName: 'Participant',
        field: 'participantName',
        width: 150,
        pinned: 'left',
        cellRenderer: (params: ICellRendererParams) => {
          const { data } = params;
          if (data.type === 'price') {
            return <div className="flex items-center justify-center h-full bg-blue-100 font-semibold text-blue-800">Price</div>;
          }
          if (data.type === 'totals') {
            return <div className="flex items-center justify-center h-full bg-gray-100 font-semibold text-gray-700">Totals</div>;
          }
          if (data.type === 'participant') {
            return (
              <div 
                className="flex items-center h-full px-2 cursor-pointer hover:bg-gray-50"
                onClick={() => onParticipantClick?.(data.participantId)}
              >
                <span className="font-medium text-gray-900 truncate">
                  {data.participantName}
                </span>
              </div>
            );
          }
          return <div />;
        }
      }
    ];

    // Add round columns
    rounds.forEach(round => {
      cols.push({
        colId: `round-${round.roundNumber}`,
        headerName: round.roundNumber.toString(),
        width: 80,
        headerComponent: RoundHeaderCell,
        cellRenderer: BidCell,
        cellStyle: { padding: 0 }
      });
    });

    return { rowData: rows, columnDefs: cols };
  }, [rounds, participants, onParticipantClick]);

  const context = {
    rounds,
    selectedRound,
    onRoundClick: onSelectedRoundChange
  };

  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📊</div>
          <div className="text-gray-500">Round table will appear here</div>
          <div className="text-xs text-gray-400 mt-2">
            Shows all participant bids for each round
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Participant Bids by Round</h3>
        <p className="text-sm text-gray-600">
          Click round headers to select, participant names to view details
        </p>
      </div>
      
      <BaseAgGrid
        columnDefs={columnDefs}
        rowData={rowData}
        height={height}
        width="100%"
        context={context}
        suppressAutoSize={false}
        autoRefresh={false}
        getRowHeight={() => 40}
        gridOptions={{
          suppressRowClickSelection: true,
          suppressContextMenu: true,
          suppressMovableColumns: true,
          animateRows: false,
          headerHeight: 35,
          rowBuffer: 10
        }}
        className="simulator-round-table"
      />
    </div>
  );
}
