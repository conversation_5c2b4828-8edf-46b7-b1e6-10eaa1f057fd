import React, { useState, useMemo } from 'react';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import { BaseAgGrid } from '../BaseAgGrid';
import type { ClockAuctionRound, ClockParticipant, ClockBid } from '../../types/types';

interface OrdersTableProps {
  rounds: ClockAuctionRound[];
  participants: ClockParticipant[];
  height: number;
  initialRound?: number;
}

interface OrderRow {
  id: string;
  participantId: string;
  participantName: string;
  side: 'buy' | 'sell';
  quantity: number;
  timestamp: Date | null;
  roundNumber: number;
  isRevised?: boolean;
}

// Cell renderer for side (buy/sell)
const SideCell: React.FC<ICellRendererParams> = (params) => {
  const side = params.value;
  const isBuy = side === 'buy';
  const bgColor = isBuy ? 'bg-green-100' : 'bg-red-100';
  const textColor = isBuy ? 'text-green-800' : 'text-red-800';
  const label = isBuy ? 'BUY' : 'SELL';
  
  return (
    <div className={`flex items-center justify-center h-full ${bgColor} ${textColor} font-semibold text-sm rounded px-2`}>
      {label}
    </div>
  );
};

// Cell renderer for quantity with revision indicator
const QuantityCell: React.FC<ICellRendererParams> = (params) => {
  const { data } = params;
  const quantity = params.value;
  
  return (
    <div className="flex items-center justify-center h-full">
      <span className="font-medium">{quantity}</span>
      {data.isRevised && (
        <span className="ml-2 text-xs bg-yellow-200 text-yellow-800 px-1 rounded">
          REV
        </span>
      )}
    </div>
  );
};

// Cell renderer for timestamp
const TimestampCell: React.FC<ICellRendererParams> = (params) => {
  const timestamp = params.value;

  if (!timestamp) {
    return (
      <div className="flex items-center justify-center h-full text-sm text-gray-400 font-mono">
        --:--:--
      </div>
    );
  }

  const timeString = timestamp.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  return (
    <div className="flex items-center justify-center h-full text-sm text-gray-600 font-mono">
      {timeString}
    </div>
  );
};

export function OrdersTable({
  rounds,
  participants,
  height,
  initialRound
}: OrdersTableProps) {
  
  const [selectedRound, setSelectedRound] = useState(initialRound || rounds.length);
  
  // Transform data for the selected round
  const { rowData, roundInfo } = useMemo(() => {
    if (rounds.length === 0 || selectedRound < 1 || selectedRound > rounds.length) {
      return { rowData: [], roundInfo: null };
    }

    const round = rounds[selectedRound - 1];
    const previousRound = selectedRound > 1 ? rounds[selectedRound - 2] : null;
    
    // Track previous bids to detect revisions
    const previousBids = new Map<string, ClockBid>();
    if (previousRound) {
      previousRound.bids.forEach(bid => {
        previousBids.set(bid.participantId, bid);
      });
    }
    
    const orders: OrderRow[] = round.bids.map(bid => {
      const participant = participants.find(p => p.id === bid.participantId);
      const previousBid = previousBids.get(bid.participantId);
      
      // Check if this is a revision (different from previous round)
      const isRevised = previousBid && (
        previousBid.side !== bid.side || 
        previousBid.quantity !== bid.quantity
      );
      
      return {
        id: bid.id,
        participantId: bid.participantId,
        participantName: participant?.name || bid.participantId,
        side: bid.side,
        quantity: bid.quantity,
        timestamp: bid.timestamp ? new Date(bid.timestamp) : null,
        roundNumber: bid.roundNumber,
        isRevised: isRevised || false
      };
    });

    // Sort by timestamp (handle undefined timestamps)
    orders.sort((a, b) => {
      const timeA = a.timestamp?.getTime() || 0;
      const timeB = b.timestamp?.getTime() || 0;
      return timeA - timeB;
    });

    return { 
      rowData: orders, 
      roundInfo: {
        roundNumber: round.roundNumber,
        price: round.announcedPrice,
        totalOrders: orders.length,
        buyOrders: orders.filter(o => o.side === 'buy').length,
        sellOrders: orders.filter(o => o.side === 'sell').length,
        revisions: orders.filter(o => o.isRevised).length
      }
    };
  }, [rounds, participants, selectedRound]);

  const columnDefs: ColDef[] = [
    {
      headerName: 'Time',
      field: 'timestamp',
      width: 100,
      cellRenderer: TimestampCell,
      sort: 'asc'
    },
    {
      headerName: 'Participant',
      field: 'participantName',
      width: 150,
      cellStyle: { fontWeight: 'medium' }
    },
    {
      headerName: 'Side',
      field: 'side',
      width: 80,
      cellRenderer: SideCell
    },
    {
      headerName: 'Quantity',
      field: 'quantity',
      width: 100,
      cellRenderer: QuantityCell,
      type: 'numericColumn'
    }
  ];

  if (rounds.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📋</div>
          <div className="text-gray-500">Orders table will appear here</div>
          <div className="text-xs text-gray-400 mt-2">
            Shows orders for each round with revision tracking
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Round Slider and Info */}
      <div className="mb-4 bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">Orders by Round</h3>
          {roundInfo && (
            <div className="text-sm text-gray-600">
              Round {roundInfo.roundNumber} • Price: ${roundInfo.price}
            </div>
          )}
        </div>
        
        {/* Round Slider */}
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Round:</label>
          <input
            type="range"
            min={1}
            max={rounds.length}
            value={selectedRound}
            onChange={(e) => setSelectedRound(parseInt(e.target.value))}
            className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => setSelectedRound(Math.max(1, selectedRound - 1))}
              disabled={selectedRound <= 1}
              className="px-2 py-1 bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
            >
              ←
            </button>
            <span className="font-mono bg-white px-2 py-1 rounded border min-w-[3rem] text-center">
              {selectedRound}
            </span>
            <button
              onClick={() => setSelectedRound(Math.min(rounds.length, selectedRound + 1))}
              disabled={selectedRound >= rounds.length}
              className="px-2 py-1 bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
            >
              →
            </button>
          </div>
        </div>

        {/* Round Statistics */}
        {roundInfo && (
          <div className="mt-3 grid grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-gray-900">{roundInfo.totalOrders}</div>
              <div className="text-gray-600">Total Orders</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">{roundInfo.buyOrders}</div>
              <div className="text-gray-600">Buy Orders</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-red-600">{roundInfo.sellOrders}</div>
              <div className="text-gray-600">Sell Orders</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-yellow-600">{roundInfo.revisions}</div>
              <div className="text-gray-600">Revisions</div>
            </div>
          </div>
        )}
      </div>

      {/* Orders Table */}
      <BaseAgGrid
        columnDefs={columnDefs}
        rowData={rowData}
        height={height - 180} // Account for slider and info section
        width="100%"
        suppressAutoSize={false}
        getRowHeight={() => 35}
        gridOptions={{
          suppressRowClickSelection: true,
          suppressContextMenu: true,
          suppressMovableColumns: true,
          animateRows: false,
          headerHeight: 35,
          rowBuffer: 10,
          defaultColDef: {
            sortable: true,
            resizable: true
          }
        }}
        className="orders-table"
      />
    </div>
  );
}
