import { ClockAuctionSimulator } from './simulator/ClockAuctionSimulator';
import type { 
  ClockAuctionConfig, 
  ClockAuctionResult, 
  ClockAuctionRound, 
  ClockParticipant,
  EndCondition 
} from '../types/types';

/**
 * Enhanced auction simulator that supports step-by-step execution
 * Wraps the original ClockAuctionSimulator to provide round-by-round control
 */
export class StepByStepAuctionSimulator {
  private config: ClockAuctionConfig;
  private rounds: ClockAuctionRound[] = [];
  private currentPrice: number;
  private isComplete: boolean = false;
  private endCondition: EndCondition | null = null;
  private priceDirection: 'up' | 'down' = 'up';
  private hasReversed: boolean = false;

  constructor(config: ClockAuctionConfig) {
    this.config = config;
    this.currentPrice = config.startingPrice;
  }

  /**
   * Execute the next round of the auction
   */
  async executeNextRound(): Promise<ClockAuctionRound> {
    if (this.isComplete) {
      throw new Error('Auction is already complete');
    }

    const roundNumber = this.rounds.length + 1;
    
    // Calculate bids for current price
    const bids = this.config.participants.map(participant => {
      const quantity = participant.quantityFunction(this.currentPrice);
      
      if (quantity > 0) {
        return {
          participantId: participant.id,
          side: 'sell' as const,
          quantity: quantity,
          price: this.currentPrice
        };
      } else if (quantity < 0) {
        return {
          participantId: participant.id,
          side: 'buy' as const,
          quantity: Math.abs(quantity),
          price: this.currentPrice
        };
      } else {
        return null;
      }
    }).filter(bid => bid !== null);

    // Calculate totals
    const totalBuyVolume = bids
      .filter(bid => bid.side === 'buy')
      .reduce((sum, bid) => sum + bid.quantity, 0);
    
    const totalSellVolume = bids
      .filter(bid => bid.side === 'sell')
      .reduce((sum, bid) => sum + bid.quantity, 0);

    // Generate constraints (simplified - in real auction these would be more complex)
    const constraints = this.config.participants.map(participant => ({
      participantId: participant.id,
      maxBuy: Math.abs(participant.curveParams.maxBuyQuantity),
      minBuy: 0,
      maxSell: participant.curveParams.maxSellQuantity,
      minSell: 0
    }));

    // Create round
    const round: ClockAuctionRound = {
      roundNumber,
      announcedPrice: this.currentPrice,
      bids,
      totalBuyVolume,
      totalSellVolume,
      constraints,
      timestamp: new Date()
    };

    this.rounds.push(round);

    // Check for end conditions
    const imbalance = totalBuyVolume - totalSellVolume;
    
    // End condition 1: Supply meets demand (within tolerance)
    if (Math.abs(imbalance) <= 1) {
      this.isComplete = true;
      this.endCondition = {
        type: 'equilibrium',
        finalPrice: this.currentPrice,
        matchedQuantity: Math.min(totalBuyVolume, totalSellVolume),
        reason: 'Supply and demand balanced'
      };
      return round;
    }

    // End condition 2: Maximum rounds reached
    if (roundNumber >= 20) {
      this.isComplete = true;
      this.endCondition = {
        type: 'max_rounds',
        finalPrice: this.currentPrice,
        matchedQuantity: Math.min(totalBuyVolume, totalSellVolume),
        reason: 'Maximum rounds reached'
      };
      return round;
    }

    // End condition 3: Price reversal completed and still no equilibrium
    if (this.hasReversed && roundNumber >= 10) {
      this.isComplete = true;
      this.endCondition = {
        type: 'no_convergence',
        finalPrice: this.currentPrice,
        matchedQuantity: Math.min(totalBuyVolume, totalSellVolume),
        reason: 'No convergence after price reversal'
      };
      return round;
    }

    // Determine next price
    this.updatePrice(imbalance);

    return round;
  }

  private updatePrice(imbalance: number): void {
    const priceChange = this.hasReversed 
      ? this.config.postReversalPriceChange 
      : this.config.preReversalPriceChange;

    if (this.priceDirection === 'up') {
      if (imbalance > 0) {
        // Excess demand, continue raising price
        this.currentPrice += priceChange;
      } else {
        // Excess supply, reverse direction
        this.priceDirection = 'down';
        this.hasReversed = true;
        this.currentPrice -= priceChange;
      }
    } else {
      if (imbalance < 0) {
        // Excess supply, continue lowering price
        this.currentPrice -= priceChange;
      } else {
        // Excess demand, reverse direction (shouldn't happen in normal clock auction)
        this.priceDirection = 'up';
        this.currentPrice += priceChange;
      }
    }

    // Ensure price doesn't go negative
    this.currentPrice = Math.max(0.01, this.currentPrice);
  }

  /**
   * Check if auction is complete
   */
  isAuctionComplete(): boolean {
    return this.isComplete;
  }

  /**
   * Get all rounds executed so far
   */
  getRounds(): ClockAuctionRound[] {
    return [...this.rounds];
  }

  /**
   * Get current price
   */
  getCurrentPrice(): number {
    return this.currentPrice;
  }

  /**
   * Get participants
   */
  getParticipants(): ClockParticipant[] {
    return this.config.participants;
  }

  /**
   * Get auction result (only available when complete)
   */
  getResult(): ClockAuctionResult | null {
    if (!this.isComplete || !this.endCondition) {
      return null;
    }

    // Calculate final allocations
    const lastRound = this.rounds[this.rounds.length - 1];
    const allocations = lastRound.bids.map(bid => ({
      participantId: bid.participantId,
      side: bid.side,
      quantity: bid.quantity,
      price: bid.price
    }));

    return {
      auctionId: this.config.id,
      rounds: this.rounds,
      endCondition: this.endCondition,
      allocations,
      totalVolume: Math.min(lastRound.totalBuyVolume, lastRound.totalSellVolume),
      efficiency: this.calculateEfficiency()
    };
  }

  private calculateEfficiency(): number {
    if (this.rounds.length === 0) return 0;
    
    const lastRound = this.rounds[this.rounds.length - 1];
    const maxPossibleVolume = Math.max(lastRound.totalBuyVolume, lastRound.totalSellVolume);
    const actualVolume = Math.min(lastRound.totalBuyVolume, lastRound.totalSellVolume);
    
    return maxPossibleVolume > 0 ? actualVolume / maxPossibleVolume : 0;
  }

  /**
   * Run the auction to completion (for comparison with step-by-step)
   */
  async runToCompletion(): Promise<ClockAuctionResult> {
    while (!this.isComplete) {
      await this.executeNextRound();
    }
    
    const result = this.getResult();
    if (!result) {
      throw new Error('Failed to get auction result');
    }
    
    return result;
  }

  /**
   * Reset the auction to initial state
   */
  reset(): void {
    this.rounds = [];
    this.currentPrice = this.config.startingPrice;
    this.isComplete = false;
    this.endCondition = null;
    this.priceDirection = 'up';
    this.hasReversed = false;
  }

  /**
   * Get auction status summary
   */
  getStatus() {
    return {
      roundNumber: this.rounds.length,
      currentPrice: this.currentPrice,
      priceDirection: this.priceDirection,
      hasReversed: this.hasReversed,
      isComplete: this.isComplete,
      endCondition: this.endCondition
    };
  }
}
