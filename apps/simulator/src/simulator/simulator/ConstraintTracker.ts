import type { BidderConstraints, ClockBid, ClockParticipant, PriceDirection } from "@/types/types";

/**
 * Tracks and enforces bidder rationality constraints
 * 
 * Implements the constraint rules from the auction specification:
 * - Buy quantities must decrease as prices increase
 * - Sell quantities must increase as prices increase  
 * - Cross-bid constraints (can't buy above sell price, etc.)
 * - Dynamic constraint updates based on bids and price movements
 */
export class ConstraintTracker {
  private constraints: Map<string, BidderConstraints> = new Map();
  private participants: ClockParticipant[];

  constructor(participants: ClockParticipant[]) {
    this.participants = participants;
    this.initializeConstraints();
  }

  /**
   * Initialize constraints for all participants
   * Start with no constraints (infinite bounds)
   */
  private initializeConstraints(): void {
    // Initialize constraints for all participants
    // Set reasonable maximum quantities to prevent infinite bids
    const DEFAULT_MAX_QUANTITY = 1000;
    
    for (const participant of this.participants) {
      this.constraints.set(participant.id, {
        participantId: participant.id,
        maxBuy: DEFAULT_MAX_QUANTITY,
        minBuy: 0,
        maxSell: DEFAULT_MAX_QUANTITY,
        minSell: 0
      });
    }
  }

  /**
   * Get current constraints for all participants
   */
  getCurrentConstraints(): BidderConstraints[] {
    return Array.from(this.constraints.values());
  }

  /**
   * Get constraints for a specific participant
   */
  getParticipantConstraints(participantId: string): BidderConstraints | null {
    return this.constraints.get(participantId) || null;
  }

  /**
   * Update constraints based on bids and price movement
   * 
   * Implements the constraint update table from the specification:
   * 
   * | Order Side | Price Change | Max Buy     | Min Buy     | Min Sell    | Max Sell    |
   * |------------|--------------|-------------|-------------|-------------|-------------|
   * | Buy        | Increase     | = order_qty | unchanged   | unchanged   | unchanged   |
   * | Buy        | Decrease     | unchanged   | = order_qty | = 0         | = 0         |
   * | Sell       | Increase     | = 0         | = 0         | = order_qty | unchanged   |
   * | Sell       | Decrease     | unchanged   | unchanged   | unchanged   | = order_qty |
   */
  updateConstraints(
    bids: ClockBid[],
    currentPrice: number,
    priceDirection: PriceDirection
  ): void {
    // Group bids by participant
    const bidsByParticipant = new Map<string, ClockBid[]>();
    for (const bid of bids) {
      if (!bidsByParticipant.has(bid.participantId)) {
        bidsByParticipant.set(bid.participantId, []);
      }
      bidsByParticipant.get(bid.participantId)!.push(bid);
    }

    // Update constraints for each participant based on their bids
    for (const [participantId, participantBids] of bidsByParticipant) {
      const currentConstraints = this.constraints.get(participantId);
      if (!currentConstraints) continue;

      for (const bid of participantBids) {
        this.updateParticipantConstraints(
          currentConstraints,
          bid,
          priceDirection
        );
      }
    }

    // Log constraint updates for debugging
    this.logConstraintUpdates(priceDirection);
  }

  /**
   * Update constraints for a single participant based on their bid
   */
  private updateParticipantConstraints(
    constraints: BidderConstraints,
    bid: ClockBid,
    priceDirection: PriceDirection
  ): void {
    const { side, quantity } = bid;

    if (side === 'buy') {
      if (priceDirection === 'up') {
        // Buy + Price Increase: Max Buy = order_qty, others unchanged
        constraints.maxBuy = Math.min(constraints.maxBuy, quantity);
      } else if (priceDirection === 'down') {
        // Buy + Price Decrease: Min Buy = order_qty, zero out sell side
        constraints.minBuy = Math.max(constraints.minBuy, quantity);
        constraints.minSell = 0;
        constraints.maxSell = 0;
      }
    } else if (side === 'sell') {
      if (priceDirection === 'up') {
        // Sell + Price Increase: Zero out buy side, Min Sell = order_qty
        constraints.maxBuy = 0;
        constraints.minBuy = 0;
        constraints.minSell = Math.max(constraints.minSell, quantity);
      } else if (priceDirection === 'down') {
        // Sell + Price Decrease: Max Sell = order_qty, others unchanged
        constraints.maxSell = Math.min(constraints.maxSell, quantity);
      }
    }

    // Ensure constraints remain valid (min <= max)
    this.validateConstraints(constraints);
  }

  /**
   * Validate that constraints are logically consistent
   */
  private validateConstraints(constraints: BidderConstraints): void {
    // Ensure min <= max for both buy and sell
    if (constraints.minBuy > constraints.maxBuy) {
      console.warn(`⚠️  Invalid buy constraints for ${constraints.participantId}: min(${constraints.minBuy}) > max(${constraints.maxBuy})`);
      constraints.maxBuy = constraints.minBuy;
    }

    if (constraints.minSell > constraints.maxSell) {
      console.warn(`⚠️  Invalid sell constraints for ${constraints.participantId}: min(${constraints.minSell}) > max(${constraints.maxSell})`);
      constraints.maxSell = constraints.minSell;
    }

    // Ensure non-negative values
    constraints.minBuy = Math.max(0, constraints.minBuy);
    constraints.maxBuy = Math.max(0, constraints.maxBuy);
    constraints.minSell = Math.max(0, constraints.minSell);
    constraints.maxSell = Math.max(0, constraints.maxSell);
  }

  /**
   * Check if a proposed bid violates constraints
   */
  isValidBid(participantId: string, side: 'buy' | 'sell', quantity: number): boolean {
    const constraints = this.constraints.get(participantId);
    if (!constraints) return false;

    if (side === 'buy') {
      return quantity >= constraints.minBuy && quantity <= constraints.maxBuy;
    } else {
      return quantity >= constraints.minSell && quantity <= constraints.maxSell;
    }
  }

  /**
   * Get valid quantity range for a participant and side
   */
  getValidQuantityRange(participantId: string, side: 'buy' | 'sell'): { min: number; max: number } {
    const constraints = this.constraints.get(participantId);
    if (!constraints) {
      return { min: 0, max: 0 };
    }

    if (side === 'buy') {
      return { min: constraints.minBuy, max: constraints.maxBuy };
    } else {
      return { min: constraints.minSell, max: constraints.maxSell };
    }
  }

  /**
   * Check if participant can bid on a given side
   */
  canBidOnSide(participantId: string, side: 'buy' | 'sell'): boolean {
    const range = this.getValidQuantityRange(participantId, side);
    return range.max > 0;
  }

  /**
   * Log constraint updates for debugging
   */
  private logConstraintUpdates(priceDirection: PriceDirection): void {
    console.log(`  📋 Constraint Updates (Price ${priceDirection}):`);
    
    for (const constraints of this.constraints.values()) {
      const participant = this.participants.find(p => p.id === constraints.participantId);
      const name = participant?.name || constraints.participantId;
      
      console.log(`    👤 ${name}:`);
      console.log(`      📈 Buy: [${constraints.minBuy}, ${constraints.maxBuy === Infinity ? '∞' : constraints.maxBuy}]`);
      console.log(`      📉 Sell: [${constraints.minSell}, ${constraints.maxSell === Infinity ? '∞' : constraints.maxSell}]`);
    }
  }

  /**
   * Reset constraints for testing or new auction
   */
  resetConstraints(): void {
    this.initializeConstraints();
  }

  /**
   * Get constraint summary for analysis
   */
  getConstraintSummary(): { [participantId: string]: BidderConstraints } {
    const summary: { [participantId: string]: BidderConstraints } = {};
    
    for (const [participantId, constraints] of this.constraints) {
      summary[participantId] = { ...constraints };
    }
    
    return summary;
  }
}