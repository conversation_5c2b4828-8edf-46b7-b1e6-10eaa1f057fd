import { v4 as uuidv4 } from 'uuid';
import type {
  ClockAuctionConfig,
  ClockAuctionRound,
  ClockAuctionResult,
  PriceDirection,
  AuctionEndCondition,
  PricePoint
} from '@/types/types';
import { ClockBidGenerator } from '../generators/ClockBidGenerator';
import { ConstraintTracker } from './ConstraintTracker';
import { AllocationEngine } from './AllocationEngine';

/**
 * Double-Sided Price-Reversing Clock-Auction Simulator
 * 
 * Implements the specific auction rules:
 * - Price announcement mechanism (auctioneer announces prices)
 * - Quantity-only bidding (participants submit quantities, not prices)
 * - Rationality constraints (enforce monotonic demand/supply curves)
 * - Price reversal detection and auction ending logic
 * - Allocation based on first round with maximum matched quantity
 */
export class ClockAuctionSimulator {
  private config: ClockAuctionConfig;
  private bidGenerator: ClockBidGenerator;
  private constraintTracker: ConstraintTracker;
  private allocationEngine: AllocationEngine;
  private rounds: ClockAuctionRound[] = [];
  private currentPrice: number;
  private priceDirection: PriceDirection = 'up';
  private hasReversed: boolean = false;
  private preReversalPrice: number | null = null;

  constructor(config: ClockAuctionConfig) {
    this.config = config;
    this.currentPrice = config.startingPrice;
    this.bidGenerator = new ClockBidGenerator(config.participants);
    this.constraintTracker = new ConstraintTracker(config.participants);
    this.allocationEngine = new AllocationEngine();
  }

  /**
   * Run the complete clock auction simulation
   */
  async simulate(): Promise<ClockAuctionResult> {
    const startTime = Date.now();
    
    console.log(`🕐 Starting Clock Auction: ${this.config.name}`);
    console.log(`📊 Starting Price: $${this.currentPrice}`);
    console.log(`👥 Participants: ${this.config.participants.length}`);
    
    let roundNumber = 1;
    let endCondition: AuctionEndCondition | null = null;

    // Main auction loop - continues until ending conditions are met
    while (!endCondition) {
      console.log(`\n🔄 Round ${roundNumber} - Price: $${this.currentPrice}`);
      
      // Create and run round
      const round = await this.runRound(roundNumber);
      this.rounds.push(round);
      
      // Check for ending conditions
      endCondition = this.checkEndingConditions(round);
      
      if (!endCondition) {
        // Update price for next round
        this.updatePrice(round);
        roundNumber++;
      }
    }

    // Determine allocation round and perform allocations
    const allocationRound = this.determineAllocationRound();
    const allocations = this.allocationEngine.allocate(
      this.rounds[allocationRound - 1],
      this.rounds[allocationRound - 1].announcedPrice
    );

    const duration = Date.now() - startTime;
    
    console.log(`\n✅ Auction Complete!`);
    console.log(`🏁 End Condition: ${endCondition.type}`);
    console.log(`💰 Final Price: $${endCondition.finalPrice}`);
    console.log(`📦 Matched Quantity: ${endCondition.matchedQuantity}`);
    console.log(`🎯 Allocation Round: ${allocationRound}`);

    return {
      auctionId: this.config.id,
      rounds: this.rounds,
      endCondition,
      allocationRound,
      allocations,
      priceHistory: this.generatePriceHistory(),
      duration
    };
  }

  /**
   * Run a single auction round
   */
  private async runRound(roundNumber: number): Promise<ClockAuctionRound> {
    const roundId = uuidv4();
    const startTime = new Date();
    
    // Get current constraints for all participants
    const constraints = this.constraintTracker.getCurrentConstraints();
    
    // Generate bids based on announced price and constraints
    const bids = await this.bidGenerator.generateBids(
      roundNumber,
      this.currentPrice,
      constraints
    );
    
    // Calculate total volumes
    const totalBuyVolume = bids
      .filter(bid => bid.side === 'buy')
      .reduce((sum, bid) => sum + bid.quantity, 0);
    
    const totalSellVolume = bids
      .filter(bid => bid.side === 'sell')
      .reduce((sum, bid) => sum + bid.quantity, 0);
    
    // Update constraints based on this round's bids
    this.constraintTracker.updateConstraints(bids, this.currentPrice, this.priceDirection);
    
    const endTime = new Date(startTime.getTime() + this.config.roundDuration);
    
    console.log(`  📈 Buy Volume: ${totalBuyVolume}`);
    console.log(`  📉 Sell Volume: ${totalSellVolume}`);
    console.log(`  ⚖️  Difference: ${totalBuyVolume - totalSellVolume}`);
    
    return {
      id: roundId,
      roundNumber,
      announcedPrice: this.currentPrice,
      startTime,
      endTime,
      status: 'completed',
      bids,
      totalBuyVolume,
      totalSellVolume,
      constraints: [...constraints] // Copy constraints at time of round
    };
  }

  /**
   * Check if auction should end based on the rules
   */
  private checkEndingConditions(round: ClockAuctionRound): AuctionEndCondition | null {
    const { totalBuyVolume, totalSellVolume } = round;
    
    // No Activity Ending: no bids from either side
    if (totalBuyVolume === 0 && totalSellVolume === 0) {
      return {
        type: 'equilibrium',
        roundNumber: round.roundNumber,
        finalPrice: round.announcedPrice,
        matchedQuantity: 0
      };
    }
    
    // Equal Activity Ending: buy volume = sell volume
    if (totalBuyVolume === totalSellVolume && totalBuyVolume > 0) {
      return {
        type: 'equilibrium',
        roundNumber: round.roundNumber,
        finalPrice: round.announcedPrice,
        matchedQuantity: totalBuyVolume
      };
    }
    
    // Check for overshoot (activity ratio inversion)
    if (this.rounds.length >= 2) {
      const previousRound = this.rounds[this.rounds.length - 2];
      const previousDominant = previousRound.totalBuyVolume > previousRound.totalSellVolume ? 'buy' : 'sell';
      const currentDominant = totalBuyVolume > totalSellVolume ? 'buy' : 'sell';
      
      // If dominant side switched, we have an overshoot
      if (previousDominant !== currentDominant) {
        if (!this.hasReversed) {
          // First reversal - mark it and continue
          this.hasReversed = true;
          this.preReversalPrice = previousRound.announcedPrice;
          console.log(`  🔄 Price reversal detected at $${this.preReversalPrice}`);
          return null;
        } else {
          // Second reversal or return to pre-reversal price
          return {
            type: 'overshoot',
            roundNumber: round.roundNumber,
            finalPrice: round.announcedPrice,
            matchedQuantity: Math.min(totalBuyVolume, totalSellVolume)
          };
        }
      }
    }
    
    // Check if we've returned to pre-reversal price (must end before reaching it)
    if (this.hasReversed && this.preReversalPrice !== null) {
      const currentIncrement = this.hasReversed ? this.config.postReversalPriceChange : this.config.preReversalPriceChange;
      if (Math.abs(round.announcedPrice - this.preReversalPrice) < currentIncrement / 2) {
        return {
          type: 'overshoot',
          roundNumber: round.roundNumber,
          finalPrice: round.announcedPrice,
          matchedQuantity: Math.min(totalBuyVolume, totalSellVolume)
        };
      }
    }
    
    return null;
  }

  /**
   * Update price for next round based on buy/sell volume imbalance
   * Uses different increments before and after price reversal
   */
  private updatePrice(round: ClockAuctionRound): void {
    const { totalBuyVolume, totalSellVolume } = round;
    
    // Determine which increment to use
    const increment = this.hasReversed ? this.config.postReversalPriceChange : this.config.preReversalPriceChange;
    
    // Check for price direction reversal
    const previousDirection = this.priceDirection;
    
    if (totalBuyVolume > totalSellVolume) {
      // More buyers than sellers - increase price
      this.currentPrice += increment;
      this.priceDirection = 'up';
    } else if (totalSellVolume > totalBuyVolume) {
      // More sellers than buyers - decrease price
      this.currentPrice -= increment;
      this.priceDirection = 'down';
    } else {
      // Equal volumes - price stays same (shouldn't happen due to ending condition)
      this.priceDirection = 'stable';
    }
    
    // Detect price reversal
    if (!this.hasReversed && previousDirection !== 'stable' && this.priceDirection !== previousDirection) {
      this.hasReversed = true;
      this.preReversalPrice = round.announcedPrice;
      console.log(`  🔄 Price reversal detected! Pre-reversal price: $${this.preReversalPrice}`);
      console.log(`  📉 Switching to smaller increments: $${this.config.postReversalPriceChange}`);
    }
    
    console.log(`  ➡️  Next price: $${this.currentPrice} (${this.priceDirection})`);
  }

  /**
   * Determine which round to use for allocation
   * Rule: "Take the first round that achieved the highest total matched quantity"
   */
  private determineAllocationRound(): number {
    let maxMatchedQuantity = 0;
    let allocationRound = 1;
    
    for (const round of this.rounds) {
      const matchedQuantity = Math.min(round.totalBuyVolume, round.totalSellVolume);
      if (matchedQuantity > maxMatchedQuantity) {
        maxMatchedQuantity = matchedQuantity;
        allocationRound = round.roundNumber;
      }
    }
    
    return allocationRound;
  }

  /**
   * Generate price history for analysis
   */
  private generatePriceHistory(): PricePoint[] {
    return this.rounds.map(round => ({
      roundNumber: round.roundNumber,
      price: round.announcedPrice,
      buyVolume: round.totalBuyVolume,
      sellVolume: round.totalSellVolume,
      direction: this.determinePriceDirection(round)
    }));
  }

  /**
   * Determine price direction for a given round
   */
  private determinePriceDirection(round: ClockAuctionRound): PriceDirection {
    if (round.roundNumber === 1) return 'stable';
    
    const previousRound = this.rounds[round.roundNumber - 2];
    if (round.announcedPrice > previousRound.announcedPrice) return 'up';
    if (round.announcedPrice < previousRound.announcedPrice) return 'down';
    return 'stable';
  }
}