import type {
  ClockAuctionRound,
  ClockBid,
  Allocation
} from '@/types/types';

/**
 * Handles final allocation of matched quantities
 * 
 * Implements allocation rules from the specification:
 * - All allocations at uniform price (from the allocation round)
 * - Either timestamp-based matching or minimum cost maximum flow
 * - Ensures fair allocation when demand/supply exceeds available quantity
 */
export class AllocationEngine {
  
  /**
   * Allocate matched quantities from the specified round
   */
  allocate(round: ClockAuctionRound, uniformPrice: number): Allocation[] {
    const buyBids = round.bids.filter(bid => bid.side === 'buy');
    const sellBids = round.bids.filter(bid => bid.side === 'sell');
    
    const totalBuyQuantity = buyBids.reduce((sum, bid) => sum + bid.quantity, 0);
    const totalSellQuantity = sellBids.reduce((sum, bid) => sum + bid.quantity, 0);
    
    // Maximum quantity that can be matched
    const maxMatchableQuantity = Math.min(totalBuyQuantity, totalSellQuantity);
    
    console.log(`\n🎯 Allocation Engine:`);
    console.log(`  📊 Total Buy Quantity: ${totalBuyQuantity}`);
    console.log(`  📊 Total Sell Quantity: ${totalSellQuantity}`);
    console.log(`  🎯 Matchable Quantity: ${maxMatchableQuantity}`);
    console.log(`  💰 Uniform Price: $${uniformPrice}`);
    
    if (maxMatchableQuantity === 0) {
      console.log(`  ❌ No quantity to allocate`);
      return [];
    }
    
    // Use timestamp-based allocation (simpler than min-cost max-flow)
    return this.timestampBasedAllocation(
      buyBids,
      sellBids,
      maxMatchableQuantity,
      uniformPrice
    );
  }
  
  /**
   * Allocate using timestamp-based matching (first-in-first-out)
   */
  private timestampBasedAllocation(
    buyBids: ClockBid[],
    sellBids: ClockBid[],
    maxMatchableQuantity: number,
    uniformPrice: number
  ): Allocation[] {
    const allocations: Allocation[] = [];
    
    // Sort bids by timestamp (earliest first)
    const sortedBuyBids = [...buyBids].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const sortedSellBids = [...sellBids].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    let remainingToAllocate = maxMatchableQuantity;
    let buyIndex = 0;
    let sellIndex = 0;
    let buyRemaining = sortedBuyBids.length > 0 ? sortedBuyBids[0].quantity : 0;
    let sellRemaining = sortedSellBids.length > 0 ? sortedSellBids[0].quantity : 0;
    
    console.log(`  🔄 Starting timestamp-based allocation...`);
    
    while (remainingToAllocate > 0 && buyIndex < sortedBuyBids.length && sellIndex < sortedSellBids.length) {
      const currentBuyBid = sortedBuyBids[buyIndex];
      const currentSellBid = sortedSellBids[sellIndex];
      
      // Determine how much to allocate in this match
      const allocationQuantity = Math.min(buyRemaining, sellRemaining, remainingToAllocate);
      
      if (allocationQuantity > 0) {
        // Create buy allocation
        allocations.push({
          participantId: currentBuyBid.participantId,
          side: 'buy',
          quantity: allocationQuantity,
          price: uniformPrice
        });
        
        // Create sell allocation
        allocations.push({
          participantId: currentSellBid.participantId,
          side: 'sell',
          quantity: allocationQuantity,
          price: uniformPrice
        });
        
        console.log(`    ✅ Matched ${allocationQuantity} units: ${this.getParticipantName(currentBuyBid)} ↔ ${this.getParticipantName(currentSellBid)}`);
        
        // Update remaining quantities
        remainingToAllocate -= allocationQuantity;
        buyRemaining -= allocationQuantity;
        sellRemaining -= allocationQuantity;
      }
      
      // Move to next bid if current one is fully allocated
      if (buyRemaining <= 0) {
        buyIndex++;
        buyRemaining = buyIndex < sortedBuyBids.length ? sortedBuyBids[buyIndex].quantity : 0;
      }
      
      if (sellRemaining <= 0) {
        sellIndex++;
        sellRemaining = sellIndex < sortedSellBids.length ? sortedSellBids[sellIndex].quantity : 0;
      }
    }
    
    console.log(`  ✅ Allocation complete: ${allocations.length / 2} matches, ${maxMatchableQuantity - remainingToAllocate} units allocated`);
    
    return allocations;
  }
  
  /**
   * Alternative allocation using minimum cost maximum flow
   * (More complex but potentially fairer)
   */
  private minCostMaxFlowAllocation(
    buyBids: ClockBid[],
    sellBids: ClockBid[],
    maxMatchableQuantity: number,
    uniformPrice: number
  ): Allocation[] {
    // This would implement a more sophisticated matching algorithm
    // For now, we'll use the simpler timestamp-based approach
    console.log(`  🔧 Min-cost max-flow allocation not yet implemented, using timestamp-based`);
    return this.timestampBasedAllocation(buyBids, sellBids, maxMatchableQuantity, uniformPrice);
  }
  
  /**
   * Get participant name for logging (simplified)
   */
  private getParticipantName(bid: ClockBid): string {
    return bid.participantId.substring(0, 8) + '...';
  }
  
  /**
   * Validate allocations for consistency
   */
  validateAllocations(allocations: Allocation[]): boolean {
    const buyAllocations = allocations.filter(a => a.side === 'buy');
    const sellAllocations = allocations.filter(a => a.side === 'sell');
    
    const totalBuyQuantity = buyAllocations.reduce((sum, a) => sum + a.quantity, 0);
    const totalSellQuantity = sellAllocations.reduce((sum, a) => sum + a.quantity, 0);
    
    if (Math.abs(totalBuyQuantity - totalSellQuantity) > 0.001) {
      console.error(`❌ Allocation validation failed: buy(${totalBuyQuantity}) != sell(${totalSellQuantity})`);
      return false;
    }
    
    // Check that all allocations have the same price (uniform pricing)
    const prices = [...new Set(allocations.map(a => a.price))];
    if (prices.length > 1) {
      console.error(`❌ Allocation validation failed: non-uniform pricing detected: ${prices}`);
      return false;
    }
    
    console.log(`✅ Allocation validation passed: ${totalBuyQuantity} units at uniform price $${prices[0]}`);
    return true;
  }
  
  /**
   * Get allocation summary by participant
   */
  getAllocationSummary(allocations: Allocation[]): { [participantId: string]: { buy: number; sell: number; netQuantity: number; value: number } } {
    const summary: { [participantId: string]: { buy: number; sell: number; netQuantity: number; value: number } } = {};
    
    for (const allocation of allocations) {
      if (!summary[allocation.participantId]) {
        summary[allocation.participantId] = { buy: 0, sell: 0, netQuantity: 0, value: 0 };
      }
      
      const participant = summary[allocation.participantId];
      
      if (allocation.side === 'buy') {
        participant.buy += allocation.quantity;
        participant.netQuantity += allocation.quantity;
        participant.value -= allocation.quantity * allocation.price; // Negative because buying costs money
      } else {
        participant.sell += allocation.quantity;
        participant.netQuantity -= allocation.quantity;
        participant.value += allocation.quantity * allocation.price; // Positive because selling earns money
      }
    }
    
    return summary;
  }
}