/**
 * Utility functions for linear demand/supply curves
 */

import type{ LinearCurveParams, QuantityFunction } from '@/types/types';

/**
 * Creates a quantity function from linear curve parameters
 * 
 * The linear curve is defined by:
 * - quantity = slope * (price - zeroPrice)
 * - Clamped between maxBuyQuantity (negative) and maxSellQuantity (positive)
 * - Quantities are rounded down to nearest integer
 * 
 * @param params Linear curve parameters
 * @returns Function that takes price and returns quantity
 */
export function createQuantityFunction(params: LinearCurveParams): QuantityFunction {
  const { maxBuyQuantity, maxSellQuantity, slope, zeroPrice } = params;
  
  return (price: number): number => {
    // Calculate raw quantity using linear equation: q = slope * (p - zeroPrice)
    const rawQuantity = slope * (price - zeroPrice);
    
    // Clamp between max buy (negative) and max sell (positive)
    const clampedQuantity = Math.max(
      maxBuyQuantity,  // Most negative (max buy)
      Math.min(maxSellQuantity, rawQuantity)  // Most positive (max sell)
    );
    
    // Round towards zero to nearest integer (floor for positive, ceil for negative)
    const result = clampedQuantity >= 0 ? Math.floor(clampedQuantity) : Math.ceil(clampedQuantity);
    // Ensure we return positive zero instead of negative zero
    return result === 0 ? 0 : result;
  };
}

/**
 * Creates a participant with both curve parameters and quantity function
 * 
 * @param id Participant ID
 * @param name Participant name
 * @param maxBuyQuantity Maximum buy quantity (negative value, e.g., -50)
 * @param maxSellQuantity Maximum sell quantity (positive value, e.g., 50)
 * @param slope Quantity/price slope
 * @param zeroPrice Price at which quantity = 0
 * @returns Complete participant object
 */
export function createParticipant(
  id: string,
  name: string,
  maxBuyQuantity: number,
  maxSellQuantity: number,
  slope: number,
  zeroPrice: number
) {
  const curveParams: LinearCurveParams = {
    maxBuyQuantity,
    maxSellQuantity,
    slope,
    zeroPrice
  };
  
  const quantityFunction = createQuantityFunction(curveParams);
  
  return {
    id,
    name,
    curveParams,
    quantityFunction
  };
}

/**
 * Validates curve parameters
 * 
 * @param params Curve parameters to validate
 * @throws Error if parameters are invalid
 */
export function validateCurveParams(params: LinearCurveParams): void {
  const { maxBuyQuantity, maxSellQuantity, slope, zeroPrice } = params;
  
  if (maxBuyQuantity > 0) {
    throw new Error('maxBuyQuantity must be negative or zero');
  }
  
  if (maxSellQuantity < 0) {
    throw new Error('maxSellQuantity must be positive or zero');
  }
  
  if (slope === 0) {
    throw new Error('slope cannot be zero');
  }
  
  if (zeroPrice < 0) {
    throw new Error('zeroPrice must be non-negative');
  }
}

/**
 * Gets the quantity for a participant at a given price
 * 
 * @param participant Participant with quantity function
 * @param price Price to evaluate
 * @returns Quantity (negative for buy, positive for sell)
 */
export function getQuantityAtPrice(participant: { quantityFunction: QuantityFunction }, price: number): number {
  return participant.quantityFunction(price);
}

/**
 * Determines if a participant would buy or sell at a given price
 * 
 * @param participant Participant with quantity function
 * @param price Price to evaluate
 * @returns 'buy' if quantity is negative, 'sell' if positive, null if zero
 */
export function getBidSide(participant: { quantityFunction: QuantityFunction }, price: number): 'buy' | 'sell' | null {
  const quantity = participant.quantityFunction(price);
  
  if (quantity < 0) return 'buy';
  if (quantity > 0) return 'sell';
  return null;
}