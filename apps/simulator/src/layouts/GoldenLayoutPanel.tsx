import { createComponent, type ComponentFactoryProps } from './shared/ComponentFactory';
import { useAuctionLogic } from './shared/useAuctionLogic';
import '../App.css';

function GoldenLayoutPanel() {
  const auctionLogic = useAuctionLogic();

  const props: ComponentFactoryProps = {
    simulator: auctionLogic.simulator,
    auctionResult: auctionLogic.auctionResult,
    rounds: auctionLogic.rounds,
    currentRound: auctionLogic.currentRound,
    isRunning: auctionLogic.isRunning,
    startNewAuction: auctionLogic.startNewAuction,
    runNextRound: auctionLogic.runNextRound,
    runFullAuction: auctionLogic.runFullAuction,
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      <header className="mb-4 px-4 py-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">🕐 Auction Simulator Dashboard (Golden Layout)</h1>
        <p className="text-lg text-gray-600">Double-Sided Price-Reversing Clock Auction with Golden Layout</p>
      </header>
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 mx-4">
        {createComponent('controls', props)}
      </div>
      <div className="flex-1 mx-4 mb-4 grid grid-cols-2 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4">
          {createComponent('price-timeline', props)}
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          {createComponent('participants', props)}
        </div>
      </div>
    </div>
  );
}

export default GoldenLayoutPanel;