import React, { useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';
import { GoldenLayout, LayoutConfig, ComponentContainer } from 'golden-layout';
import 'golden-layout/dist/css/goldenlayout-base.css';
import 'golden-layout/dist/css/themes/goldenlayout-dark-theme.css';
import { createComponent, useComponentProps, componentDefinitions } from './shared/ComponentFactory';
import '../App.css';

function GoldenLayoutPanel() {
  const props = useComponentProps();
  const goldenLayoutRef = useRef<HTMLDivElement>(null);
  const layoutRef = useRef<GoldenLayout | null>(null);

  useEffect(() => {
    if (!goldenLayoutRef.current) return;

    // Define the layout configuration
    const config: LayoutConfig = {
      root: {
        type: 'row',
        content: [
          {
            type: 'column',
            width: 50,
            content: [
              {
                type: 'stack',
                height: 50,
                content: [
                  { type: 'component', componentType: 'price-timeline', title: '📈 Price Timeline' },
                  { type: 'component', componentType: 'price-step-chart', title: '📊 Price Steps' },
                ]
              },
              {
                type: 'row',
                height: 50,
                content: [
                  { type: 'component', componentType: 'supply-demand', title: '📊 Supply & Demand' },
                  { type: 'component', componentType: 'volume-analysis', title: '📊 Volume Analysis' },
                ]
              }
            ]
          },
          {
            type: 'column',
            width: 50,
            content: [
              {
                type: 'row',
                height: 30,
                content: [
                  { type: 'component', componentType: 'constraint-heatmap', title: '🔥 Constraints' },
                  { type: 'component', componentType: 'participants', title: '👥 Participants' },
                ]
              },
              {
                type: 'stack',
                height: 40,
                content: [
                  { type: 'component', componentType: 'orders-table', title: '📋 Orders' },
                  { type: 'component', componentType: 'round-table', title: '📊 Round Table' },
                  { type: 'component', componentType: 'quantity-constraints', title: '📊 Constraints' },
                ]
              },
              {
                type: 'row',
                height: 30,
                content: [
                  { type: 'component', componentType: '3d-ribbon', title: '🎗️ 3D Ribbons' },
                  { type: 'component', componentType: 'sankey-flow', title: '🔄 Sankey Flow' },
                ]
              }
            ]
          }
        ]
      }
    };

    // Create the Golden Layout instance
    const layout = new GoldenLayout(goldenLayoutRef.current);
    layoutRef.current = layout;

    // Register component factory
    componentDefinitions.forEach(comp => {
      layout.registerComponentFactoryFunction(comp.id, (container: ComponentContainer) => {
        const element = document.createElement('div');
        element.style.padding = '8px';
        element.style.height = '100%';
        element.style.overflow = 'auto';
        element.setAttribute('data-react-root', 'true');

        // Create React component and render it using React 18 createRoot
        const reactElement = createComponent(comp.id, props);
        const root = createRoot(element);

        // Render immediately - components should handle their own visibility checks
        root.render(reactElement);

        // Store the root for cleanup
        (element as any).__reactRoot = root;

        return element;
      });
    });

    // Load the layout
    layout.loadLayout(config);

    return () => {
      if (layoutRef.current) {
        // Clean up React roots before destroying layout
        const allElements = goldenLayoutRef.current?.querySelectorAll('[data-react-root]');
        allElements?.forEach(element => {
          const root = (element as any).__reactRoot;
          if (root) {
            root.unmount();
          }
        });

        layoutRef.current.destroy();
      }
    };
  }, [props]);

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 mx-4">
        {createComponent('controls', props)}
      </div>
      <div
        ref={goldenLayoutRef}
        style={{
          height: '800px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          margin: '0 1rem',
          background: '#fff'
        }}
      />
    </div>
  );
}

export default GoldenLayoutPanel;