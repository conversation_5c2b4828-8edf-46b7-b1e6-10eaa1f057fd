import React from 'react';

interface AppShellProps {
  toolbar: React.ReactNode;
  main: React.ReactNode;
}

function AppShell({ toolbar, main }: AppShellProps) {
  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <header style={{
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        background: '#fff',
        borderBottom: '1px solid #ddd',
        flexShrink: 0
      }}>
        {toolbar}
      </header>
      <main style={{
        flex: 1,
        overflowY: 'auto',
        minHeight: 0
      }}>
        {main}
      </main>
    </div>
  );
}

export default AppShell;