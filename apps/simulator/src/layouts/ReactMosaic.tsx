import React, { useState } from 'react';
import { Mosaic, MosaicWindow, type MosaicNode, type MosaicBranch } from 'react-mosaic-component';
import 'react-mosaic-component/react-mosaic-component.css';
import { createComponent, type ComponentFactoryProps, componentDefinitions } from './shared/ComponentFactory';
import { useAuctionLogic } from './shared/useAuctionLogic';
import '../App.css';

// Define the mosaic window types
type ViewId = string;

function ReactMosaicPanel() {
  const auctionLogic = useAuctionLogic();

  const props: ComponentFactoryProps = {
    simulator: auctionLogic.simulator,
    auctionResult: auctionLogic.auctionResult,
    rounds: auctionLogic.rounds,
    currentRound: auctionLogic.currentRound,
    isRunning: auctionLogic.isRunning,
    startNewAuction: auctionLogic.startNewAuction,
    runNextRound: auctionLogic.runNextRound,
    runFullAuction: auctionLogic.runFullAuction,
  };

  // Create initial mosaic layout - more windows to demonstrate drag-to-tab
  const [currentNode, setCurrentNode] = useState<MosaicNode<ViewId> | null>({
    direction: 'row',
    first: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'price-timeline',
        second: 'price-step-chart',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'supply-demand',
        second: 'volume-analysis',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    second: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'participants',
        second: 'constraint-heatmap',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'orders-table',
        second: 'round-table',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    splitPercentage: 60,
  });

  // Available components that can be added to the mosaic
  const availableComponents = componentDefinitions.filter(comp => comp.id !== 'controls');
  let componentIndex = 0;

  const createNode = (): ViewId => {
    // Cycle through available components when creating new windows
    const component = availableComponents[componentIndex % availableComponents.length];
    componentIndex++;
    return component.id;
  };

  const renderTile = (id: ViewId, path: MosaicBranch[]) => {
    const component = componentDefinitions.find(comp => comp.id === id);
    const title = component ? component.name : id;

    return (
      <MosaicWindow<ViewId>
        path={path}
        title={title}
        createNode={createNode}
        toolbarControls={[]} // Remove all toolbar buttons for clean interface
        renderToolbar={() => (
          <div style={{
            padding: '6px 12px',
            fontSize: '13px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderBottom: '1px solid #ddd',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontWeight: '500'
          }}>
            <span>{title}</span>
            <span style={{ fontSize: '10px', opacity: 0.8 }}>
              📊
            </span>
          </div>
        )}
      >
        <div style={{ padding: '12px', height: '100%', overflow: 'auto' }}>
          {createComponent(id, props)}
        </div>
      </MosaicWindow>
    );
  };

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>
      <header className="mb-4 px-4 pt-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">🕐 Auction Simulator Dashboard (React Mosaic)</h1>
        <p className="text-lg text-gray-600">Double-Sided Price-Reversing Clock Auction with React Mosaic Layout</p>
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-sm font-semibold text-green-800 mb-1">🎯 Clean React Mosaic Layout:</h3>
          <ul className="text-xs text-green-700 space-y-1">
            <li>• <strong>Clean Interface:</strong> Minimal toolbars with gradient headers</li>
            <li>• <strong>Resize Panes:</strong> Drag the splitter bars between windows</li>
            <li>• <strong>Fixed Layout:</strong> 8 components in 2×4 mosaic grid</li>
            <li>• <strong>Full Width:</strong> Uses entire browser width</li>
            <li>• <strong>Note:</strong> Prioritizes clean UI over advanced controls</li>
          </ul>
        </div>
      </header>
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 mx-4">
        {createComponent('controls', props)}
      </div>
      <div style={{ height: '800px', border: '1px solid #ddd', borderRadius: '8px', margin: '0 1rem' }}>
        <Mosaic<ViewId>
          renderTile={renderTile}
          value={currentNode}
          onChange={setCurrentNode}
          className="mosaic-blueprint-theme"
          zeroStateView={
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#666',
              fontSize: '16px'
            }}>
              <div style={{ marginBottom: '16px', fontSize: '48px' }}>📊</div>
              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>React Mosaic Dashboard</div>
              <div style={{ textAlign: 'center', lineHeight: '1.5' }}>
                Drag windows to create tabs by dropping them on title bars<br/>
                Split windows by dropping on edges<br/>
                Right-click for more options
              </div>
            </div>
          }
        />
      </div>
    </div>
  );
}

export default ReactMosaicPanel;