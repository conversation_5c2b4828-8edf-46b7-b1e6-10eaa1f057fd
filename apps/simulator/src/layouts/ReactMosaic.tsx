import React, { useState } from 'react';
import { Mosaic, MosaicWindow, type MosaicNode, type MosaicBranch } from 'react-mosaic-component';
import 'react-mosaic-component/react-mosaic-component.css';
import { createComponent, type ComponentFactoryProps, componentDefinitions } from './shared/ComponentFactory';
import { useAuctionLogic } from './shared/useAuctionLogic';
import '../App.css';

// Define the mosaic window types
type ViewId = string;

function ReactMosaicPanel() {
  const auctionLogic = useAuctionLogic();

  const props: ComponentFactoryProps = {
    simulator: auctionLogic.simulator,
    auctionResult: auctionLogic.auctionResult,
    rounds: auctionLogic.rounds,
    currentRound: auctionLogic.currentRound,
    isRunning: auctionLogic.isRunning,
    startNewAuction: auctionLogic.startNewAuction,
    runNextRound: auctionLogic.runNextRound,
    runFullAuction: auctionLogic.runFullAuction,
  };

  // Create initial mosaic layout - more windows to demonstrate drag-to-tab
  const [currentNode, setCurrentNode] = useState<MosaicNode<ViewId> | null>({
    direction: 'row',
    first: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'price-timeline',
        second: 'price-step-chart',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'supply-demand',
        second: 'volume-analysis',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    second: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'participants',
        second: 'constraint-heatmap',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'orders-table',
        second: 'round-table',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    splitPercentage: 60,
  });

  // Available components that can be added to the mosaic
  const availableComponents = componentDefinitions.filter(comp => comp.id !== 'controls');
  let componentIndex = 0;

  const createNode = (): ViewId => {
    // Cycle through available components when creating new windows
    const component = availableComponents[componentIndex % availableComponents.length];
    componentIndex++;
    return component.id;
  };

  const renderTile = (id: ViewId, path: MosaicBranch[]) => {
    const component = componentDefinitions.find(comp => comp.id === id);
    const title = component ? component.name : id;

    return (
      <MosaicWindow<ViewId>
        path={path}
        title={title}
        createNode={createNode}
        // Use default toolbar for proper drag-and-drop functionality
      >
        <div style={{ padding: '8px', height: '100%', overflow: 'auto' }}>
          <div style={{
            marginBottom: '8px',
            fontSize: '11px',
            color: '#666',
            textAlign: 'center',
            background: '#f8f9fa',
            padding: '4px',
            borderRadius: '4px'
          }}>
            💡 Use the toolbar above to split/expand/close windows
          </div>
          {createComponent(id, props)}
        </div>
      </MosaicWindow>
    );
  };

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>
      <header className="mb-4 px-4 pt-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">🕐 Auction Simulator Dashboard (React Mosaic)</h1>
        <p className="text-lg text-gray-600">Double-Sided Price-Reversing Clock Auction with React Mosaic Layout</p>
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="text-sm font-semibold text-yellow-800 mb-1">🎯 React Mosaic Controls:</h3>
          <ul className="text-xs text-yellow-700 space-y-1">
            <li>• <strong>Split Window:</strong> Click the "Split Window" button in each window's toolbar</li>
            <li>• <strong>Expand:</strong> Click "Expand" to maximize a window</li>
            <li>• <strong>Close:</strong> Click "Close Window" to remove a window</li>
            <li>• <strong>Resize:</strong> Drag the splitter bars between windows</li>
            <li>• <strong>Note:</strong> Drag-and-drop may not work with custom toolbars</li>
          </ul>
        </div>
      </header>
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 mx-4">
        {createComponent('controls', props)}
      </div>
      <div style={{ height: '800px', border: '1px solid #ddd', borderRadius: '8px', margin: '0 1rem' }}>
        <Mosaic<ViewId>
          renderTile={renderTile}
          value={currentNode}
          onChange={setCurrentNode}
          className="mosaic-blueprint-theme"
          zeroStateView={
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#666',
              fontSize: '16px'
            }}>
              <div style={{ marginBottom: '16px', fontSize: '48px' }}>📊</div>
              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>React Mosaic Dashboard</div>
              <div style={{ textAlign: 'center', lineHeight: '1.5' }}>
                Drag windows to create tabs by dropping them on title bars<br/>
                Split windows by dropping on edges<br/>
                Right-click for more options
              </div>
            </div>
          }
        />
      </div>
    </div>
  );
}

export default ReactMosaicPanel;