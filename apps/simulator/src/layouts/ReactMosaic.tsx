import React, { useState } from 'react';
import { <PERSON>sai<PERSON>, MosaicWindow, type MosaicNode, type MosaicBranch } from 'react-mosaic-component';
import 'react-mosaic-component/react-mosaic-component.css';
import { createComponent, useComponentProps, componentDefinitions } from './shared/ComponentFactory';
import '../App.css';

// Define the mosaic window types
type ViewId = string;

function ReactMosaicPanel() {
  const props = useComponentProps();

  // Create initial mosaic layout - more windows to demonstrate drag-to-tab
  const [currentNode, setCurrentNode] = useState<MosaicNode<ViewId> | null>({
    direction: 'row',
    first: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'price-timeline',
        second: 'price-step-chart',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'supply-demand',
        second: 'volume-analysis',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    second: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'participants',
        second: 'constraint-heatmap',
        splitPercentage: 50,
      },
      second: {
        direction: 'row',
        first: 'orders-table',
        second: 'round-table',
        splitPercentage: 50,
      },
      splitPercentage: 50,
    },
    splitPercentage: 60,
  });

  // Available components that can be added to the mosaic
  const availableComponents = componentDefinitions.filter(comp => comp.id !== 'controls');
  let componentIndex = 0;

  const createNode = (): ViewId => {
    // Cycle through available components when creating new windows
    const component = availableComponents[componentIndex % availableComponents.length];
    componentIndex++;
    return component.id;
  };

  const renderTile = (id: ViewId, path: MosaicBranch[]) => {
    const component = componentDefinitions.find(comp => comp.id === id);
    const title = component ? component.name : id;

    return (
      <MosaicWindow<ViewId>
        path={path}
        title={title}
        createNode={createNode}
        toolbarControls={[]} // Remove all toolbar buttons for clean interface
        renderToolbar={() => (
          <div style={{
            padding: '6px 12px',
            fontSize: '13px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderBottom: '1px solid #ddd',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontWeight: '500'
          }}>
            <span>{title}</span>
            <span style={{ fontSize: '10px', opacity: 0.8 }}>
              📊
            </span>
          </div>
        )}
      >
        <div style={{ padding: '12px', height: '100%', overflow: 'auto' }}>
          {createComponent(id, props)}
        </div>
      </MosaicWindow>
    );
  };

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>

      <div style={{ height: '800px', border: '1px solid #ddd', borderRadius: '8px', margin: '0 1rem', color: '#374151' }}>
        <Mosaic<ViewId>
          renderTile={renderTile}
          value={currentNode}
          onChange={setCurrentNode}
          className="mosaic-blueprint-theme"
          zeroStateView={
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#666',
              fontSize: '16px'
            }}>
              <div style={{ marginBottom: '16px', fontSize: '48px' }}>📊</div>
              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>React Mosaic Dashboard</div>
              <div style={{ textAlign: 'center', lineHeight: '1.5' }}>
                Drag windows to create tabs by dropping them on title bars<br/>
                Split windows by dropping on edges<br/>
                Right-click for more options
              </div>
            </div>
          }
        />
      </div>
    </div>
  );
}

export default ReactMosaicPanel;