import { useState, useCallback } from 'react';
import { ResizableDashboard } from '../components/ResizableDashboard';
import { StepByStepAuctionSimulator } from '../simulator/StepByStepAuctionSimulator';
import { createParticipant } from '../simulator/utils/curveUtils';
import type { ClockAuctionConfig, ClockAuctionResult, ClockAuctionRound } from '../types/types';
import '../App.css';

function App() {
  console.log('App component rendering...');

  const [simulator, setSimulator] = useState<StepByStepAuctionSimulator | null>(null);
  const [auctionResult, setAuctionResult] = useState<ClockAuctionResult | null>(null);
  const [currentRound, setCurrentRound] = useState<number>(0);
  const [isRunning, setIsRunning] = useState(false);
  const [rounds, setRounds] = useState<ClockAuctionRound[]>([]);

  // Create a sample auction configuration
  const createSampleAuction = useCallback((): ClockAuctionConfig => {
    const participants = [
      // Aggressive buyer - pension fund
      createParticipant(
        'buyer-1',
        'Pension Fund Alpha',
        -50,  // maxBuyQuantity (negative for buyers)
        0,    // maxSellQuantity
        2.5,  // slope (price sensitivity)
        120   // zeroPrice (price at zero quantity)
      ),

      // Conservative buyer - insurance company
      createParticipant(
        'buyer-2',
        'Insurance Corp',
        -30,
        0,
        1.8,
        110
      ),

      // Energy producer - main seller
      createParticipant(
        'seller-1',
        'Energy Producer Ltd',
        0,    // maxBuyQuantity
        60,   // maxSellQuantity (positive for sellers)
        2.2,  // slope
        70    // zeroPrice
      ),

      // Small producer
      createParticipant(
        'seller-2',
        'Green Energy Co',
        0,
        25,
        1.5,
        80
      ),

      // Market maker - can buy or sell
      createParticipant(
        'trader-1',
        'Market Maker Inc',
        -20,  // Can buy up to 20
        20,   // Can sell up to 20
        2.0,  // Moderate sensitivity
        95    // Market-neutral zero price
      )
    ];

    return {
      id: 'demo-auction',
      name: 'Double-Sided Clock Auction Demo',
      startingPrice: 85,
      preReversalPriceChange: 3,   // Price change before reversal
      postReversalPriceChange: 1,  // Price change after reversal (smaller)
      roundDuration: 2000,         // 2 seconds per round
      participants
    };
  }, []);

  const startNewAuction = useCallback(async () => {
    console.log('🚀 Starting new auction...');
    setIsRunning(true);
    setCurrentRound(0);
    setRounds([]);
    setAuctionResult(null);

    const config = createSampleAuction();
    const newSimulator = new StepByStepAuctionSimulator(config);
    setSimulator(newSimulator);
    setIsRunning(false);
  }, [createSampleAuction]);

  const runFullAuction = useCallback(async () => {
    if (!simulator) return;

    setIsRunning(true);
    try {
      const result = await simulator.runToCompletion();
      setAuctionResult(result);
      setRounds(result.rounds);
      setCurrentRound(result.rounds.length);
    } catch (error) {
      console.error('Error running full auction:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator]);

  const runNextRound = useCallback(async () => {
    if (!simulator || isRunning) return;

    setIsRunning(true);
    try {
      // Check if auction is already complete
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) {
          setAuctionResult(result);
        }
        setIsRunning(false);
        return;
      }

      // Execute next round
      await simulator.executeNextRound();
      const allRounds = simulator.getRounds();
      setRounds(allRounds);
      setCurrentRound(allRounds.length);

      // Check if auction completed after this round
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) {
          setAuctionResult(result);
        }
      }
    } catch (error) {
      console.error('Error running next round:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator, isRunning]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div>
        <header className="mb-8 px-4">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            🕐 Auction Simulator Dashboard
          </h1>
          <p className="text-lg text-gray-600">
            Double-Sided Price-Reversing Clock Auction with Real-Time Visualization
          </p>
        </header>

        {/* Control Panel */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8 mx-4">
          <div className="flex flex-wrap gap-4 items-center">
            <button
              onClick={startNewAuction}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
            >
              🚀 Start New Auction
            </button>

            {simulator && !auctionResult && (
              <>
                <button
                  onClick={runNextRound}
                  disabled={isRunning || simulator.isAuctionComplete()}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
                >
                  ⏭️ Next Round
                </button>

                <button
                  onClick={runFullAuction}
                  disabled={isRunning || simulator.isAuctionComplete()}
                  className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
                >
                  ⚡ Run to Completion
                </button>
              </>
            )}

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="font-medium">Round:</span>
              <span className="bg-gray-100 px-2 py-1 rounded">{currentRound}</span>
              {isRunning && (
                <span className="text-blue-600 animate-pulse">● Running...</span>
              )}
            </div>
          </div>
        </div>

        {/* Dashboard */}
        <ResizableDashboard
          rounds={rounds}
          auctionResult={auctionResult}
          currentRound={currentRound}
          participants={simulator?.getParticipants() || []}
        />
      </div>
    </div>
  );
}

export default App;
