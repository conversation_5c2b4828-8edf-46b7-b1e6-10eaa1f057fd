
import { useState, useCallback } from 'react';
import { Layout, Model, TabNode } from 'flexlayout-react';
import type { IJsonModel } from 'flexlayout-react';
import 'flexlayout-react/style/light.css';
import { createComponent, useComponentProps } from './shared/ComponentFactory';
import '../App.css';

const LAYOUT_STORAGE_KEY = 'flexlayout-auction-simulator';

// Default layout configuration
const defaultLayoutConfig: IJsonModel = {
  global: {
    tabEnableClose: false,
    tabEnableRename: false,
    tabSetEnableTabStrip: true,
    tabSetEnableDrop: true,
    tabSetEnableDrag: true,
    tabSetEnableMaximize: true,
    borderEnableDrop: true
  },
  borders: [
    {
      type: "border",
      location: "bottom",
      size: 100,
      children: [
        {
          type: "tab",
          name: "Controls",
          component: "controls",
          enableClose: false
        }
      ]
    }
  ],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "📈 Price Timeline",
            component: "price-timeline"
          },
          {
            type: "tab",
            name: "📊 Price Steps",
            component: "price-steps"
          }
        ]
      },
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "📊 Supply & Demand",
            component: "supply-demand"
          },
          {
            type: "tab",
            name: "📊 Volume Analysis",
            component: "volume-analysis"
          }
        ]
      },
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "🔥 Constraints",
            component: "constraints"
          },
          {
            type: "tab",
            name: "👥 Participants",
            component: "participants"
          },
          {
            type: "tab",
            name: "📋 Orders",
            component: "orders"
          },
          {
            type: "tab",
            name: "📊 Round Table",
            component: "round-table"
          },
          {
            type: "tab",
            name: "📊 Quantity Constraints",
            component: "quantity-constraints"
          },
          {
            type: "tab",
            name: "🎗️ 3D Ribbons",
            component: "3d-ribbons"
          },
          {
            type: "tab",
            name: "🔄 Allocations",
            component: "allocations"
          }
        ]
      }
    ]
  }
};

function FlexLayoutPanel() {
  const props = useComponentProps();
  const [model, setModel] = useState<Model>(() => {
    // Try to load layout from localStorage
    const savedLayout = localStorage.getItem(LAYOUT_STORAGE_KEY);
    if (savedLayout) {
      try {
        return Model.fromJson(JSON.parse(savedLayout));
      } catch (error) {
        console.warn('Failed to load saved layout, using default:', error);
      }
    }
    return Model.fromJson(defaultLayoutConfig);
  });

  // Save layout to localStorage whenever it changes
  const handleModelChange = useCallback((newModel: Model) => {
    setModel(newModel);
    try {
      localStorage.setItem(LAYOUT_STORAGE_KEY, JSON.stringify(newModel.toJson()));
    } catch (error) {
      console.warn('Failed to save layout to localStorage:', error);
    }
  }, []);

  // Factory function to create components for tabs
  const factory = useCallback((node: TabNode) => {
    const component = node.getComponent();
    return createComponent(component || 'unknown', props);
  }, [props]);

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>
      <div className="mx-4" style={{ minHeight: "600px" }}>
        <Layout
          model={model}
          factory={factory}
          onModelChange={handleModelChange}
        />
      </div>
    </div>
  );
}

export default FlexLayoutPanel;