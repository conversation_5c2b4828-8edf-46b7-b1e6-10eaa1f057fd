
import { useState, useCallback } from 'react';
import { Layout, Model, TabNode } from 'flexlayout-react';
import type { IJsonModel } from 'flexlayout-react';
import 'flexlayout-react/style/light.css';
import { StepByStepAuctionSimulator } from '../simulator/StepByStepAuctionSimulator';
import { createParticipant } from '../simulator/utils/curveUtils';
import { PriceTimelineChart } from '../components/charts/PriceTimelineChart';
import { PriceTimelineStepChart } from '../components/charts/PriceTimelineStepChart';
import { SupplyDemandChart } from '../components/charts/SupplyDemandChart';
import { VolumeAnalysisChart } from '../components/charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from '../components/charts/ConstraintHeatmap';
import { AllocationFlowChart } from '../components/charts/AllocationFlowChart';
import { SankeyAllocationFlow } from '../components/charts/SankeyAllocationFlow';
import { ParticipantsTable } from '../components/ParticipantsTable';
import { SimulatorRoundTable } from '../components/tables/SimulatorRoundTable';
import { OrdersTable } from '../components/tables/OrdersTable';
import { QuantityConstraintsWidget } from '../components/widgets/QuantityConstraintsWidget';
import { RibbonChart3D } from '../components/charts/RibbonChart3D';
import type { ClockAuctionConfig, ClockAuctionResult, ClockAuctionRound } from '../types/types';
import '../App.css';

const LAYOUT_STORAGE_KEY = 'flexlayout-auction-simulator';

// Default layout configuration
const defaultLayoutConfig: IJsonModel = {
  global: {
    tabEnableClose: false,
    tabEnableRename: false,
    tabSetEnableTabStrip: true,
    tabSetEnableDrop: true,
    tabSetEnableDrag: true,
    tabSetEnableMaximize: true,
    borderEnableDrop: true
  },
  borders: [
    {
      type: "border",
      location: "bottom",
      size: 100,
      children: [
        {
          type: "tab",
          name: "Controls",
          component: "controls",
          enableClose: false
        }
      ]
    }
  ],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "📈 Price Timeline",
            component: "price-timeline"
          },
          {
            type: "tab",
            name: "📊 Price Steps",
            component: "price-steps"
          }
        ]
      },
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "📊 Supply & Demand",
            component: "supply-demand"
          },
          {
            type: "tab",
            name: "📊 Volume Analysis",
            component: "volume-analysis"
          }
        ]
      },
      {
        type: "tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            name: "🔥 Constraints",
            component: "constraints"
          },
          {
            type: "tab",
            name: "👥 Participants",
            component: "participants"
          },
          {
            type: "tab",
            name: "📋 Orders",
            component: "orders"
          },
          {
            type: "tab",
            name: "📊 Round Table",
            component: "round-table"
          },
          {
            type: "tab",
            name: "📊 Quantity Constraints",
            component: "quantity-constraints"
          },
          {
            type: "tab",
            name: "🎗️ 3D Ribbons",
            component: "3d-ribbons"
          },
          {
            type: "tab",
            name: "🔄 Allocations",
            component: "allocations"
          }
        ]
      }
    ]
  }
};

function FlexLayoutPanel() {
  const [simulator, setSimulator] = useState<StepByStepAuctionSimulator | null>(null);
  const [auctionResult, setAuctionResult] = useState<ClockAuctionResult | null>(null);
  const [currentRound, setCurrentRound] = useState<number>(0);
  const [isRunning, setIsRunning] = useState(false);
  const [rounds, setRounds] = useState<ClockAuctionRound[]>([]);
  const [model, setModel] = useState<Model>(() => {
    // Try to load layout from localStorage
    const savedLayout = localStorage.getItem(LAYOUT_STORAGE_KEY);
    if (savedLayout) {
      try {
        return Model.fromJson(JSON.parse(savedLayout));
      } catch (error) {
        console.warn('Failed to load saved layout, using default:', error);
      }
    }
    return Model.fromJson(defaultLayoutConfig);
  });

  // Save layout to localStorage whenever it changes
  const handleModelChange = useCallback((newModel: Model) => {
    setModel(newModel);
    try {
      localStorage.setItem(LAYOUT_STORAGE_KEY, JSON.stringify(newModel.toJson()));
    } catch (error) {
      console.warn('Failed to save layout to localStorage:', error);
    }
  }, []);

  // Create a sample auction configuration
  const createSampleAuction = useCallback((): ClockAuctionConfig => {
    const participants = [
      // Aggressive buyer - pension fund
      createParticipant(
        'buyer-1',
        'Pension Fund Alpha',
        -50,  // maxBuyQuantity (negative for buyers)
        0,    // maxSellQuantity
        2.5,  // slope (price sensitivity)
        120   // zeroPrice (price at zero quantity)
      ),

      // Conservative buyer - insurance company
      createParticipant(
        'buyer-2',
        'Insurance Corp',
        -30,
        0,
        1.8,
        110
      ),

      // Energy producer - main seller
      createParticipant(
        'seller-1',
        'Energy Producer Ltd',
        0,    // maxBuyQuantity
        60,   // maxSellQuantity (positive for sellers)
        2.2,  // slope
        70    // zeroPrice
      ),

      // Small producer
      createParticipant(
        'seller-2',
        'Green Energy Co',
        0,
        25,
        1.5,
        80
      ),

      // Market maker - can buy or sell
      createParticipant(
        'trader-1',
        'Market Maker Inc',
        -20,  // Can buy up to 20
        20,   // Can sell up to 20
        2.0,  // Moderate sensitivity
        95    // Market-neutral zero price
      )
    ];

    return {
      id: 'demo-auction',
      name: 'Double-Sided Clock Auction Demo',
      startingPrice: 85,
      preReversalPriceChange: 3,   // Price change before reversal
      postReversalPriceChange: 1,  // Price change after reversal (smaller)
      roundDuration: 2000,         // 2 seconds per round
      participants
    };
  }, []);

  const startNewAuction = useCallback(async () => {
    console.log('🚀 Starting new auction...');
    setIsRunning(true);
    setCurrentRound(0);
    setRounds([]);
    setAuctionResult(null);

    const config = createSampleAuction();
    const newSimulator = new StepByStepAuctionSimulator(config);
    setSimulator(newSimulator);
    setIsRunning(false);
  }, [createSampleAuction]);

  const runFullAuction = useCallback(async () => {
    if (!simulator) return;

    setIsRunning(true);
    try {
      const result = await simulator.runToCompletion();
      setAuctionResult(result);
      setRounds(result.rounds);
      setCurrentRound(result.rounds.length);
    } catch (error) {
      console.error('Error running full auction:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator]);

  const runNextRound = useCallback(async () => {
    if (!simulator || isRunning) return;

    setIsRunning(true);
    try {
      // Check if auction is already complete
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) {
          setAuctionResult(result);
        }
        setIsRunning(false);
        return;
      }

      // Execute next round
      await simulator.executeNextRound();
      const allRounds = simulator.getRounds();
      setRounds(allRounds);
      setCurrentRound(allRounds.length);

      // Check if auction completed after this round
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) {
          setAuctionResult(result);
        }
      }
    } catch (error) {
      console.error('Error running next round:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator, isRunning]);

  // Factory function to create components for tabs
  const factory = useCallback((node: TabNode) => {
    const component = node.getComponent();
    const participants = simulator?.getParticipants() || [];
    const latestRound = rounds[rounds.length - 1];
    const currentPrice = latestRound?.announcedPrice || 0;

    switch (component) {
      case 'controls':
        return (
          <div className="bg-white p-4 h-full">
            <div className="flex flex-wrap gap-4 items-center">
              <button
                onClick={startNewAuction}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
              >
                🚀 Start New Auction
              </button>

              {simulator && !auctionResult && (
                <>
                  <button
                    onClick={runNextRound}
                    disabled={isRunning || simulator.isAuctionComplete()}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
                  >
                    ⏭️ Next Round
                  </button>

                  <button
                    onClick={runFullAuction}
                    disabled={isRunning || simulator.isAuctionComplete()}
                    className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
                  >
                    ⚡ Run to Completion
                  </button>
                </>
              )}

              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="font-medium">Round:</span>
                <span className="bg-gray-100 px-2 py-1 rounded">{currentRound}</span>
                {isRunning && (
                  <span className="text-blue-600 animate-pulse">● Running...</span>
                )}
              </div>
            </div>
          </div>
        );

      case 'price-timeline':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <PriceTimelineChart rounds={rounds} height={300} showVolumes={true} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see price timeline
          </div>
        );

      case 'price-steps':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <PriceTimelineStepChart rounds={rounds} height={300} showRoundOverlay={true} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see price steps
          </div>
        );

      case 'supply-demand':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <SupplyDemandChart
              participants={participants}
              rounds={rounds}
              currentPrice={currentPrice}
              height={300}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see supply & demand
          </div>
        );

      case 'volume-analysis':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <VolumeAnalysisChart
              rounds={rounds}
              participants={participants}
              height={300}
              chartType="stacked"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see volume analysis
          </div>
        );

      case 'constraints':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <ConstraintHeatmap
              rounds={rounds}
              participants={participants}
              height={300}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see constraints
          </div>
        );

      case 'participants':
        return participants.length > 0 ? (
          <div className="p-4 h-full">
            <ParticipantsTable
              participants={participants}
              rounds={rounds}
              currentPrice={currentPrice}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see participants
          </div>
        );

      case 'orders':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <OrdersTable
              rounds={rounds}
              participants={participants}
              height={300}
              initialRound={rounds.length}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see orders
          </div>
        );

      case 'round-table':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <SimulatorRoundTable
              rounds={rounds}
              participants={participants}
              height={300}
              selectedRound={rounds.length}
              onSelectedRoundChange={(round) => console.log('Selected round:', round)}
              onParticipantClick={(participantId) => console.log('Participant clicked:', participantId)}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see round data
          </div>
        );

      case 'quantity-constraints':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <QuantityConstraintsWidget
              rounds={rounds}
              participants={participants}
              height={300}
              onParticipantSelect={(participantId) => console.log('Participant selected:', participantId)}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see quantity constraints
          </div>
        );

      case '3d-ribbons':
        return rounds.length > 0 ? (
          <div className="p-4 h-full">
            <RibbonChart3D
              rounds={rounds}
              participants={participants}
              width={600}
              height={400}
              showBuyRibbons={true}
              showSellRibbons={true}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Start an auction to see 3D ribbons
          </div>
        );

      case 'allocations':
        if (auctionResult) {
          return (
            <div className="p-4 h-full">
              <AllocationFlowChart
                auctionResult={auctionResult}
                participants={participants}
                height={300}
              />
            </div>
          );
        } else if (rounds.length > 0) {
          return (
            <div className="p-4 h-full">
              <SankeyAllocationFlow
                auctionResult={null}
                participants={participants}
                rounds={rounds}
                height={300}
              />
            </div>
          );
        } else {
          return (
            <div className="flex items-center justify-center h-full text-gray-500">
              Start an auction to see allocations
            </div>
          );
        }

      default:
        return <div className="flex items-center justify-center h-full text-gray-500">Unknown component: {component}</div>;
    }
  }, [simulator, auctionResult, rounds, currentRound, isRunning, startNewAuction, runNextRound, runFullAuction]);

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      <header className="mb-8 px-4 py-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          🕐 Auction Simulator Dashboard (FlexLayout)
        </h1>
        <p className="text-lg text-gray-600">
          Double-Sided Price-Reversing Clock Auction with Flexible Layout
        </p>
      </header>

      <div className="flex-1 mx-4">
        <Layout
          model={model}
          factory={factory}
          onModelChange={handleModelChange}
        />
      </div>
    </div>
  );
}

export default FlexLayoutPanel;
  