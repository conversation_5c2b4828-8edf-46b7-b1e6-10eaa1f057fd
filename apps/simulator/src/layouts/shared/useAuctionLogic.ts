import { useState, useCallback } from 'react';
import { StepByStepAuctionSimulator } from '../../simulator/StepByStepAuctionSimulator';
import { createParticipant } from '../../simulator/utils/curveUtils';
import type { ClockAuctionConfig, ClockAuctionResult, ClockAuctionRound } from '../../types/types';

export function useAuctionLogic() {
  const [simulator, setSimulator] = useState<StepByStepAuctionSimulator | null>(null);
  const [auctionResult, setAuctionResult] = useState<ClockAuctionResult | null>(null);
  const [currentRound, setCurrentRound] = useState<number>(0);
  const [isRunning, setIsRunning] = useState(false);
  const [rounds, setRounds] = useState<ClockAuctionRound[]>([]);

  const createSampleAuction = useCallback((): ClockAuctionConfig => {
    const participants = [
      createParticipant('buyer-1', 'Pension Fund Alpha', -50, 0, 2.5, 120),
      createParticipant('seller-1', 'Energy Producer Ltd', 0, 60, 2.2, 70),
    ];
    return {
      id: 'demo-auction',
      name: 'Double-Sided Clock Auction Demo',
      startingPrice: 85,
      preReversalPriceChange: 3,
      postReversalPriceChange: 1,
      roundDuration: 2000,
      participants
    };
  }, []);

  const startNewAuction = useCallback(async () => {
    setIsRunning(true);
    setCurrentRound(0);
    setRounds([]);
    setAuctionResult(null);
    const config = createSampleAuction();
    const newSimulator = new StepByStepAuctionSimulator(config);
    setSimulator(newSimulator);
    setIsRunning(false);
  }, [createSampleAuction]);

  const runFullAuction = useCallback(async () => {
    if (!simulator) return;
    setIsRunning(true);
    try {
      const result = await simulator.runToCompletion();
      setAuctionResult(result);
      setRounds(result.rounds);
      setCurrentRound(result.rounds.length);
    } catch (error) {
      console.error('Error running full auction:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator]);

  const runNextRound = useCallback(async () => {
    if (!simulator || isRunning) return;
    setIsRunning(true);
    try {
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) setAuctionResult(result);
        setIsRunning(false);
        return;
      }
      await simulator.executeNextRound();
      const allRounds = simulator.getRounds();
      setRounds(allRounds);
      setCurrentRound(allRounds.length);
      if (simulator.isAuctionComplete()) {
        const result = simulator.getResult();
        if (result) setAuctionResult(result);
      }
    } catch (error) {
      console.error('Error running next round:', error);
    } finally {
      setIsRunning(false);
    }
  }, [simulator, isRunning]);

  return { simulator, auctionResult, currentRound, isRunning, rounds, startNewAuction, runNextRound, runFullAuction };
}