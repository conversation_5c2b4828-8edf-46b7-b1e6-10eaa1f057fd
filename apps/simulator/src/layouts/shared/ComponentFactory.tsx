import { StepByStepAuctionSimulator } from '../../simulator/StepByStepAuctionSimulator';
import { PriceTimelineChart } from '../../components/charts/PriceTimelineChart';
import { PriceTimelineStepChart } from '../../components/charts/PriceTimelineStepChart';
import { SupplyDemandChart } from '../../components/charts/SupplyDemandChart';
import { VolumeAnalysisChart } from '../../components/charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from '../../components/charts/ConstraintHeatmap';
import { AllocationFlowChart } from '../../components/charts/AllocationFlowChart';
import { SankeyAllocationFlow } from '../../components/charts/SankeyAllocationFlow';
import { ParticipantsTable } from '../../components/ParticipantsTable';
import { SimulatorRoundTable } from '../../components/tables/SimulatorRoundTable';
import { OrdersTable } from '../../components/tables/OrdersTable';
import { QuantityConstraintsWidget } from '../../components/widgets/QuantityConstraintsWidget';
import { RibbonChart3D } from '../../components/charts/RibbonChart3D';
import type { ClockAuctionResult, ClockAuctionRound } from '../../types/types';

export interface ComponentFactoryProps {
  simulator: StepByStepAuctionSimulator | null;
  auctionResult: ClockAuctionResult | null;
  rounds: ClockAuctionRound[];
  currentRound: number;
  isRunning: boolean;
  startNewAuction: () => void;
  runNextRound: () => void;
  runFullAuction: () => void;
}

export function createComponent(componentType: string, props: ComponentFactoryProps): JSX.Element {
  const { simulator, auctionResult, rounds, currentRound, isRunning, startNewAuction, runNextRound, runFullAuction } = props;
  const participants = simulator?.getParticipants() || [];
  const latestRound = rounds[rounds.length - 1];
  const currentPrice = latestRound?.announcedPrice || 0;

  switch (componentType) {
    case 'controls':
      return (
        <div className="bg-white p-4 h-full">
          <div className="flex flex-wrap gap-4 items-center">
            <button onClick={startNewAuction} disabled={isRunning} className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              🚀 Start New Auction
            </button>
            <button onClick={runNextRound} disabled={!simulator || isRunning || simulator.isAuctionComplete()} className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              ⏭️ Next Round
            </button>
            <button onClick={runFullAuction} disabled={!simulator || isRunning || simulator.isAuctionComplete()} className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              🏃‍♂️ Run to Completion
            </button>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="font-medium">Round:</span>
              <span className="bg-gray-100 px-2 py-1 rounded">{currentRound}</span>
              {isRunning && <span className="text-blue-600 animate-pulse">● Running...</span>}
            </div>
          </div>
        </div>
      );
    case 'price-timeline':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><PriceTimelineChart rounds={rounds} height={300} showVolumes={true} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see price timeline</div>
      );
    case 'participants':
      return participants.length > 0 ? (
        <div className="p-4 h-full"><ParticipantsTable participants={participants} rounds={rounds} currentPrice={currentPrice} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see participants</div>
      );
    // Add more cases for other components as needed
    default:
      return <div className="flex items-center justify-center h-full text-gray-500">Component: {componentType}</div>;
  }
}

export const componentDefinitions = [
  { id: 'controls', name: '🎮 Controls', category: 'control' },
  { id: 'price-timeline', name: '📈 Price Timeline', category: 'chart' },
  { id: 'participants', name: '👥 Participants', category: 'table' },
];