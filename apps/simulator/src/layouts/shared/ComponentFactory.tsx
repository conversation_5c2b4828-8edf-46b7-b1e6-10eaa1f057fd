import { useMemo } from 'react';
import { StepByStepAuctionSimulator } from '../../simulator/StepByStepAuctionSimulator';
import { PriceTimelineChart } from '../../components/charts/PriceTimelineChart';
import { PriceTimelineStepChart } from '../../components/charts/PriceTimelineStepChart';
import { SupplyDemandChart } from '../../components/charts/SupplyDemandChart';
import { VolumeAnalysisChart } from '../../components/charts/VolumeAnalysisChart';
import { ConstraintHeatmap } from '../../components/charts/ConstraintHeatmap';
import { AllocationFlowChart } from '../../components/charts/AllocationFlowChart';
import { SankeyAllocationFlow } from '../../components/charts/SankeyAllocationFlow';
import { ParticipantsTable } from '../../components/ParticipantsTable';
import { SimulatorRoundTable } from '../../components/tables/SimulatorRoundTable';
import { OrdersTable } from '../../components/tables/OrdersTable';
import { QuantityConstraintsWidget } from '../../components/widgets/QuantityConstraintsWidget';
import { RibbonChart3D } from '../../components/charts/RibbonChart3D';
import { useAuctionStore } from '../../store/useAuctionStore';
import type { ClockAuctionResult, ClockAuctionRound } from '../../types/types';

export interface ComponentFactoryProps {
  simulator: StepByStepAuctionSimulator | null;
  auctionResult: ClockAuctionResult | null;
  rounds: ClockAuctionRound[];
  currentRound: number;
  isRunning: boolean;
  startNewAuction: () => void;
  runNextRound: () => void;
  runFullAuction: () => void;
}

/**
 * Custom hook that provides all the props needed for component creation.
 * Uses Valtio store for shared auction state across all layouts.
 */
export function useComponentProps(): ComponentFactoryProps {
  const store = useAuctionStore();

  return useMemo(() => ({
    simulator: store.simulator,
    auctionResult: store.auctionResult,
    rounds: store.rounds,
    currentRound: store.currentRound,
    isRunning: store.isRunning,
    startNewAuction: store.startNewAuction,
    runNextRound: store.runNextRound,
    runFullAuction: store.runFullAuction,
  }), [store]);
}

export function createComponent(componentType: string, props: ComponentFactoryProps): JSX.Element {
  const { simulator, auctionResult, rounds, currentRound, isRunning, startNewAuction, runNextRound, runFullAuction } = props;
  const participants = simulator?.getParticipants() || [];
  const latestRound = rounds[rounds.length - 1];
  const currentPrice = latestRound?.announcedPrice || 0;

  switch (componentType) {
    case 'controls':
      return (
        <div className="bg-white p-4 h-full">
          <div className="flex flex-wrap gap-4 items-center">
            <button onClick={startNewAuction} disabled={isRunning} className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              🚀 Start New Auction
            </button>
            <button onClick={runNextRound} disabled={!simulator || isRunning || simulator.isAuctionComplete()} className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              ⏭️ Next Round
            </button>
            <button onClick={runFullAuction} disabled={!simulator || isRunning || simulator.isAuctionComplete()} className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              🏃‍♂️ Run to Completion
            </button>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="font-medium">Round:</span>
              <span className="bg-gray-100 px-2 py-1 rounded">{currentRound}</span>
              {isRunning && <span className="text-blue-600 animate-pulse">● Running...</span>}
            </div>
          </div>
        </div>
      );
    case 'price-timeline':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><PriceTimelineChart rounds={rounds} height={300} showVolumes={true} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see price timeline</div>
      );
    case 'participants':
      return participants.length > 0 ? (
        <div className="p-4 h-full"><ParticipantsTable participants={participants} rounds={rounds} currentPrice={currentPrice} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see participants</div>
      );
    case 'price-step-chart':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><PriceTimelineStepChart rounds={rounds} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see price steps</div>
      );
    case 'supply-demand':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><SupplyDemandChart rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see supply & demand</div>
      );
    case 'volume-analysis':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><VolumeAnalysisChart rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see volume analysis</div>
      );
    case 'constraint-heatmap':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><ConstraintHeatmap rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see constraints</div>
      );
    case 'allocation-flow':
      return auctionResult ? (
        <div className="p-4 h-full"><AllocationFlowChart auctionResult={auctionResult} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Complete auction to see final allocations</div>
      );
    case 'sankey-flow':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><SankeyAllocationFlow auctionResult={auctionResult} participants={participants} rounds={rounds} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see allocation flow</div>
      );
    case 'round-table':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><SimulatorRoundTable rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see round table</div>
      );
    case 'orders-table':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><OrdersTable rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see orders</div>
      );
    case 'quantity-constraints':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><QuantityConstraintsWidget rounds={rounds} participants={participants} height={300} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see quantity constraints</div>
      );
    case '3d-ribbon':
      return rounds.length > 0 ? (
        <div className="p-4 h-full"><RibbonChart3D rounds={rounds} participants={participants} width={400} height={300} showBuyRibbons={true} showSellRibbons={true} /></div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Start an auction to see 3D ribbons</div>
      );
    default:
      return <div className="flex items-center justify-center h-full text-gray-500">Component: {componentType}</div>;
  }
}

export const componentDefinitions = [
  { id: 'controls', name: '🎮 Controls', category: 'control' },
  { id: 'price-timeline', name: '📈 Price Timeline', category: 'chart' },
  { id: 'price-step-chart', name: '📊 Price × Quantity Steps', category: 'chart' },
  { id: 'supply-demand', name: '📊 Supply & Demand', category: 'chart' },
  { id: 'volume-analysis', name: '📊 Volume Analysis', category: 'chart' },
  { id: 'constraint-heatmap', name: '🔥 Constraint Tracking', category: 'chart' },
  { id: 'participants', name: '👥 Participants', category: 'table' },
  { id: 'round-table', name: '📊 Round Table', category: 'table' },
  { id: 'orders-table', name: '📋 Orders by Round', category: 'table' },
  { id: 'quantity-constraints', name: '📊 Quantity Constraints', category: 'widget' },
  { id: '3d-ribbon', name: '🎗️ 3D Ribbons', category: 'chart' },
  { id: 'sankey-flow', name: '🔄 Allocation Flow', category: 'chart' },
  { id: 'allocation-flow', name: '📊 Final Allocations', category: 'chart' },
];