import { chromium } from 'playwright';

async function testSimulator() {
  console.log('🚀 Starting Simulator Test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`[${type.toUpperCase()}] ${text}`);
  });
  
  // Listen for errors
  page.on('pageerror', error => {
    console.error('❌ Page Error:', error.message);
  });
  
  try {
    console.log('📱 Navigating to simulator...');
    await page.goto('http://localhost:5173');
    
    console.log('⏳ Waiting for page to load...');
    await page.waitForTimeout(2000);
    
    console.log('📸 Taking initial screenshot...');
    await page.screenshot({ path: 'simulator-initial.png', fullPage: true });
    
    console.log('🎯 Looking for Start button...');
    const startButton = await page.locator('button:has-text("Start")').first();
    if (await startButton.isVisible()) {
      console.log('✅ Start button found, clicking...');
      await startButton.click();
      await page.waitForTimeout(1000);
      
      console.log('📸 Taking screenshot after Start...');
      await page.screenshot({ path: 'simulator-after-start.png', fullPage: true });
      
      console.log('🎯 Looking for Next Round button...');
      const nextRoundButton = await page.locator('button:has-text("Next Round")').first();
      if (await nextRoundButton.isVisible()) {
        console.log('✅ Next Round button found, clicking...');
        await nextRoundButton.click();
        await page.waitForTimeout(2000);
        
        console.log('📸 Taking screenshot after Next Round...');
        await page.screenshot({ path: 'simulator-after-next-round.png', fullPage: true });
        
        console.log('🎗️ Testing 3D Ribbon Chart interaction...');
        const ribbonChart = await page.locator('.plotly-chart-container').first();
        if (await ribbonChart.isVisible()) {
          console.log('✅ 3D Ribbon Chart found, testing interaction...');
          const chartBox = await ribbonChart.boundingBox();
          if (chartBox) {
            // Try to interact with the 3D chart
            await page.mouse.move(chartBox.x + chartBox.width / 2, chartBox.y + chartBox.height / 2);
            await page.mouse.down();
            await page.mouse.move(chartBox.x + chartBox.width / 2 + 50, chartBox.y + chartBox.height / 2 + 50);
            await page.mouse.up();
            console.log('✅ 3D Chart interaction test completed');
          }
        } else {
          console.log('⚠️ 3D Ribbon Chart not found');
        }
        
        console.log('📸 Taking final screenshot...');
        await page.screenshot({ path: 'simulator-final.png', fullPage: true });
        
      } else {
        console.log('❌ Next Round button not found');
      }
    } else {
      console.log('❌ Start button not found');
    }
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testSimulator().catch(console.error);
