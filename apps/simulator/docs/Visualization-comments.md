1) status cards
- these seem to take up a lot of height, they could be a single wide card of much less height.
- in addition to current price, total buy and sell it would be nice to have smaller change indicator from prior round, ie: up or down arrow and smaller change.

2) PriceTimelineChart
- so this is a nice idea, but note, plotting round by quantities is worth keeping, but more important would be quantity by price, because of the reversal mechanism. Given that the price changes prior to reversal are large, and post reversal small, bars are problematic because the width would have to change with the smaller increment, so instead we should use a stepchart.
ie: quantity x price, as steps, but with an overlay of the round number, so that you can see when it reverses. ie: you might see 1,2,3,4,8,7,6,5 for the rounds. There is a nice example of a step chart in the apps/dashboard that you could copy over and modify for the simulator domain model.
Note: keep the current PriceTimelineChart, but add a PriceTimelineStepChart.

3) SupplyDemandChart
- I don't understand this at all.

4) VolumeAnalysisChart
- that chart is tiny, I can't see what it shows, it sounds interesting though.

5) ConstraintHeatmap
- This chart is empty, I don't know what it's supposed to show, but I'm interested.

6) AllocationFlowChart
- It's not visible anymore. it was once and that looked interesting, but note: in apps/dashboard I use a Sankey which shows which participants match with each other. Ie: in this auction we don't operate as a dealer, ie: in which participants trade with us, rather we operate as a broker, and participants trade with each other. So, take a look at the apps/dashboard sankey diagram and copy it over, using the simulator data model..

7) The Participant simulation parameters is interesting, but the table just shows the current round. Take a look at apps/dashboard at the round-table, there you can see how we show the current bid for each participant for each round.
Copy that over as well.

8) we need to see a list of the orders, for each round, ie: a table which shows the orders for one round with the ability to use a 'slider' to examine prior rounds. ie: for one round show the orders as they come in (and if they are revised during the round indicate that an order has been revised and show the new quantity).

9) We should be able to see the constraints for every participant for every round. In apps/dashboard I have a Quantity Constraints widget, which I intend to patent. Currently it is a horizontal bar from max buy to zero in the middle to max sell.
The buy bar has dark green background and the sell bar has dark red background.
Then on top of those bars we have the current buy and sell constraints in brighter green and red. So if for example trader one has initial buy and sell elibigibility of 50 (both sides), then in the first round they would have bright green buy and red sell bars. Then if whereever they place their bid, eg buy 25, we put a yellow (or other color) mark. then if the price increases then everything to the left is no longer available and that part of the bar is no longer bright green, but rather a darker green. and similarly for the sell side. There are examples in docs/design/A.auction-rules.md
So this makes it easy for the participants to understand: if the price increases then the bar to the left of their bid shrinks, and if the price decreases then the bar to the right of their bid shrinks. This also allows them to see if they will lose the ability to switch. eg if you place a buy quantity and the price decreases, then you will no longer be able to sell at that price, nor will you be able to buy less (ie: the price has decreased so you have to be willing to buy at least the same, but not less at a lower price).

Now this concept could be used in the round table as follows, if the first column is the trader name, then the second one could be these quantity constraints bars stacked vertically. That way the auctioneer should be able to easily see how the constraints are changing for each participant for each round.

10) now for the big one: the 3d ribbon chart. We have a few examples in apps/dashboard showing how we can use a 3d ribbon chart to show each trader's demand and supply curves as they are revealed during the auction. We have an example in plotly, but it's unclear if we can make it do what we need. 
The y axis is the quantity, the x axis is the price (or round, or both) and the traders are arranged on the z axis. so we have a series of plots for each trader. Now, one thought is that the Y axis goes from max buy to zero to max sell, though it's unclear we can actually do that with plotly, as we don't want to show negative numbers on the y axis.
Additionally, we'd color the ribbon (or lines) such that the buy side is green and the sell side is red, perhaps with some gradient as they approach zero.